{"name": "nutripro", "version": "1.0.0", "description": "NutriPro - Complete Point of Sale and Business Management Platform", "private": true, "workspaces": ["apps/*", "packages/*"], "packageManager": "pnpm@8.15.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "type-check": "turbo type-check", "test": "turbo test", "test:unit": "turbo test:unit", "test:integration": "turbo test:integration", "test:e2e": "turbo test:e2e", "test:coverage": "turbo test:coverage", "test:watch": "turbo test:watch", "test:ci": "turbo test:ci", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "check-all": "pnpm lint && pnpm type-check && pnpm test:ci", "db:generate": "turbo db:generate", "db:push": "turbo db:push", "db:seed": "turbo db:seed", "db:studio": "turbo db:studio", "mobile:start": "turbo mobile:start", "mobile:build": "turbo mobile:build", "mobile:submit": "turbo mobile:submit", "ai:dev": "turbo ai:dev", "ai:build": "turbo ai:build", "docs:api": "turbo docs:api", "docs:components": "turbo docs:components", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "prepare": "husky install", "analyze": "turbo analyze"}, "devDependencies": {"@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@turbo/gen": "^1.11.2", "conventional-changelog-cli": "^4.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0", "prettier": "^3.1.1", "turbo": "^1.11.2", "typescript": "^5.3.3"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml,yaml}": ["prettier --write"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["pos", "point-of-sale", "inventory", "supplements", "retail", "wholesale", "loyalty", "coach-program", "ai-forecasting"], "author": "NutriPro Team", "license": "UNLICENSED"}