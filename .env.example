# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Database
DATABASE_URL=postgresql://postgres:password@localhost:54322/postgres

# Development
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Admin Panel
NEXT_PUBLIC_ADMIN_URL=http://localhost:3000

# Wholesale Portal
NEXT_PUBLIC_WHOLESALE_URL=http://localhost:3001

# AI Backend
NEXT_PUBLIC_AI_API_URL=http://localhost:8000
GOOGLE_GEMINI_API_KEY=your_gemini_api_key

# Mobile Apps (Expo)
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_API_URL=http://localhost:3000/api
EXPO_PUBLIC_AI_API_URL=http://localhost:8000/api

# Authentication
JWT_SECRET=your_jwt_secret_key
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password

# File Storage
NEXT_PUBLIC_STORAGE_URL=your_supabase_storage_url

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_ga_id
SENTRY_DSN=your_sentry_dsn

# Payment Processing (Optional)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# External APIs (Optional)
OPENAI_API_KEY=your_openai_api_key_backup

# Development Tools
ANALYZE=false
DEBUG=false
