{"name": "@nutripro/ai-backend", "version": "0.1.0", "private": true, "description": "NutriPro AI Backend - Python FastAPI with Google Gemini integration", "scripts": {"ai:dev": "python -m uvicorn main:app --reload --port 8000", "ai:build": "python -m build", "build": "python -m build", "dev": "python -m uvicorn main:app --reload --port 8000", "start": "python -m uvicorn main:app --host 0.0.0.0 --port 8000", "test": "python -m pytest", "test:watch": "python -m pytest --watch", "test:coverage": "python -m pytest --cov=src", "lint": "python -m flake8 src", "format": "python -m black src", "type-check": "python -m mypy src", "clean": "rm -rf dist build *.egg-info __pycache__ .pytest_cache .coverage"}, "dependencies": {}, "devDependencies": {}}