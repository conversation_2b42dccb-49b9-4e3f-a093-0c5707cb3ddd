{"name": "@nutripro/wholesale-portal", "version": "0.1.0", "private": true, "description": "NutriPro Wholesale Portal - Customer self-service portal", "scripts": {"build": "next build", "dev": "next dev --port 3001", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@nutripro/ui": "workspace:*", "@nutripro/database": "workspace:*", "@nutripro/utils": "workspace:*", "@nutripro/types": "workspace:*", "@nutripro/auth": "workspace:*", "@nutripro/config": "workspace:*", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@supabase/ssr": "^0.1.0", "@tanstack/react-query": "^5.17.9", "@tanstack/react-table": "^8.11.6", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^0.2.0", "date-fns": "^3.0.6", "lucide-react": "^0.303.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@nutripro/eslint-config": "workspace:*", "@nutripro/tsconfig": "workspace:*", "@testing-library/jest-dom": "^6.1.6", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/node": "^20.10.6", "@types/react": "^18.2.46", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}