/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: [
    "@nutripro/ui",
    "@nutripro/database", 
    "@nutripro/utils",
    "@nutripro/types",
    "@nutripro/auth",
    "@nutripro/config"
  ],
  experimental: {
    optimizePackageImports: ["@nutripro/ui", "@tremor/react", "lucide-react"],
  },
  images: {
    domains: ["localhost", "supabase.co"],
    formats: ["image/webp", "image/avif"],
  },
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "Referrer-Policy",
            value: "strict-origin-when-cross-origin",
          },
        ],
      },
    ];
  },
};

// Bundle analyzer
const withBundleAnalyzer = require("@next/bundle-analyzer")({
  enabled: process.env.ANALYZE === "true",
});

module.exports = withBundleAnalyzer(nextConfig);
