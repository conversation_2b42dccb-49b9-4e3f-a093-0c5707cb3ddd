{"expo": {"name": "NutriPro POS", "slug": "nutripro-pos", "version": "1.0.0", "orientation": "landscape", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.nutripro.pos"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.nutripro.pos", "permissions": ["CAMERA", "RECORD_AUDIO", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "VIBRATE"]}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "plugins": ["expo-router", ["expo-barcode-scanner", {"cameraPermission": "Allow NutriPro POS to access camera for barcode scanning."}], ["expo-camera", {"cameraPermission": "Allow NutriPro POS to access camera for product photos."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow NutriPro POS to use your location for delivery tracking."}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "your-eas-project-id"}}}}