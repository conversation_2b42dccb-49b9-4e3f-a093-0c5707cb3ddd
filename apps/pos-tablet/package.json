{"name": "@nutripro/pos-tablet", "version": "0.1.0", "private": true, "description": "NutriPro POS Tablet - Offline-first point of sale application", "main": "expo-router/entry", "scripts": {"android": "expo run:android", "ios": "expo run:ios", "start": "expo start", "mobile:start": "expo start --dev-client", "mobile:build": "eas build", "mobile:submit": "eas submit", "build": "expo export", "dev": "expo start --dev-client", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "detox test", "test:e2e:build": "detox build", "clean": "expo r -c"}, "dependencies": {"@nutripro/utils": "workspace:*", "@nutripro/types": "workspace:*", "@nutripro/config": "workspace:*", "@expo/vector-icons": "^13.0.0", "@gluestack-ui/themed": "^1.1.14", "@react-native-async-storage/async-storage": "1.21.0", "@react-navigation/native": "^6.1.9", "@supabase/supabase-js": "^2.38.5", "@tanstack/react-query": "^5.17.9", "expo": "~50.0.0", "expo-barcode-scanner": "~12.9.0", "expo-camera": "~14.1.0", "expo-constants": "~15.4.0", "expo-dev-client": "~3.3.0", "expo-font": "~11.10.0", "expo-haptics": "~12.8.0", "expo-keep-awake": "~12.8.0", "expo-linking": "~6.2.0", "expo-location": "~16.5.0", "expo-notifications": "~0.27.0", "expo-print": "~12.8.0", "expo-router": "~3.4.0", "expo-secure-store": "~12.9.0", "expo-splash-screen": "~0.26.0", "expo-sqlite": "~13.4.0", "expo-status-bar": "~1.11.0", "expo-updates": "~0.24.0", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.48.2", "react-native": "0.73.0", "react-native-gesture-handler": "~2.14.0", "react-native-mmkv": "^2.11.0", "react-native-reanimated": "~3.6.0", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-vector-icons": "^10.0.3", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@nutripro/eslint-config": "workspace:*", "@nutripro/tsconfig": "workspace:*", "@babel/core": "^7.23.6", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.2", "@types/jest": "^29.5.11", "@types/react": "~18.2.45", "@types/react-test-renderer": "^18.0.7", "detox": "^20.13.5", "eslint": "^8.56.0", "jest": "^29.7.0", "jest-expo": "~50.0.0", "react-test-renderer": "18.2.0", "typescript": "^5.3.3"}}