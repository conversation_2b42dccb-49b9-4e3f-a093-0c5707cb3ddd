{"extends": "@nutripro/tsconfig/react-native.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}