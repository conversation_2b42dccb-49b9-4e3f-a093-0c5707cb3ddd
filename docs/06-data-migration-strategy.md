# NutriPro Data Migration Strategy

## Migration Overview

The data migration process involves transferring data from two existing systems (Loyverse POS and Zoho Books) into the unified NutriPro platform. This is a critical phase that requires careful planning to ensure zero data loss and minimal business disruption.

### Current Systems
1. **Loyverse POS**: Retail transactions, products, customers, inventory
2. **Zoho Books**: Wholesale customers, invoices, payments, accounting data

### Migration Goals
- **Zero Data Loss**: All critical business data preserved
- **Data Integrity**: Relationships and constraints maintained
- **Minimal Downtime**: Business operations continue during migration
- **Data Quality**: Clean, deduplicated, and validated data
- **Audit Trail**: Complete record of migration process

## Pre-Migration Assessment

### Data Audit Checklist

#### Loyverse Data Assessment
- [ ] **Products**: Count, categories, pricing, inventory levels
- [ ] **Customers**: Retail customer records, contact information
- [ ] **Transactions**: Sales history, payment methods, refunds
- [ ] **Inventory**: Stock movements, adjustments, current levels
- [ ] **Staff**: User accounts, permissions, activity logs
- [ ] **Settings**: Tax rates, payment methods, store configuration

#### Zoho Books Data Assessment
- [ ] **Contacts**: Wholesale customer information, credit terms
- [ ] **Items**: Product catalog, wholesale pricing
- [ ] **Invoices**: Sales invoices, payment status, history
- [ ] **Payments**: Payment records, outstanding balances
- [ ] **Reports**: Financial data, tax information

### Data Quality Analysis

#### Common Data Issues to Address
1. **Duplicate Records**: Same customers/products in both systems
2. **Inconsistent Naming**: Product names, customer names variations
3. **Missing Information**: Incomplete contact details, missing SKUs
4. **Data Format Issues**: Phone numbers, addresses, dates
5. **Orphaned Records**: References to deleted items

#### Data Cleansing Plan
```sql
-- Example data cleansing queries
-- Standardize phone numbers
UPDATE customers SET phone = REGEXP_REPLACE(phone, '[^0-9]', '', 'g');

-- Clean product names
UPDATE products SET name = TRIM(REGEXP_REPLACE(name, '\s+', ' ', 'g'));

-- Validate email addresses
UPDATE customers SET email = NULL WHERE email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
```

## Migration Strategy

### Phased Migration Approach

#### Phase 1: Data Export and Preparation (Week 1)
1. **Export Data from Source Systems**
   - Loyverse: Use export functionality or API
   - Zoho Books: Export via API or manual export
   
2. **Data Validation and Cleansing**
   - Remove duplicates
   - Standardize formats
   - Fill missing required fields
   - Validate relationships

3. **Create Migration Scripts**
   - Data transformation scripts
   - Validation scripts
   - Rollback procedures

#### Phase 2: Test Migration (Week 2)
1. **Set Up Staging Environment**
   - Mirror production database structure
   - Test migration scripts
   - Validate data integrity

2. **Perform Test Migration**
   - Run migration scripts
   - Validate data accuracy
   - Test application functionality
   - Performance testing

3. **Refine Migration Process**
   - Fix identified issues
   - Optimize performance
   - Update documentation

#### Phase 3: Production Migration (Week 3)
1. **Pre-Migration Backup**
   - Full system backup
   - Document current state
   - Prepare rollback plan

2. **Execute Migration**
   - Run migration scripts
   - Monitor progress
   - Validate results

3. **Post-Migration Validation**
   - Data integrity checks
   - Application testing
   - User acceptance testing

## Data Mapping

### Products Migration

#### Loyverse → NutriPro Products
```typescript
interface LoyverseProduct {
  item_id: string;
  item_name: string;
  price: number;
  cost: number;
  category: string;
  track_stock: boolean;
  current_stock: number;
  barcode?: string;
}

interface NutriProProduct {
  id: uuid;
  sku: string; // Generated from item_id or barcode
  name: string; // item_name
  retail_price: number; // price
  cost_price: number; // cost
  category_id: uuid; // Mapped from category
  stock_quantity: number; // current_stock
  barcode?: string;
  is_active: boolean; // true
}

// Migration mapping
const migrateProduct = (loyverseProduct: LoyverseProduct): NutriProProduct => ({
  id: generateUUID(),
  sku: loyverseProduct.barcode || `LV-${loyverseProduct.item_id}`,
  name: loyverseProduct.item_name.trim(),
  retail_price: loyverseProduct.price,
  cost_price: loyverseProduct.cost || 0,
  category_id: mapCategory(loyverseProduct.category),
  stock_quantity: loyverseProduct.current_stock || 0,
  barcode: loyverseProduct.barcode,
  is_active: true
});
```

#### Zoho Books → NutriPro Products (Wholesale)
```typescript
interface ZohoItem {
  item_id: string;
  name: string;
  rate: number;
  purchase_rate: number;
  item_type: string;
}

// Update existing products with wholesale pricing
const updateProductWithWholesale = (product: NutriProProduct, zohoItem: ZohoItem) => ({
  ...product,
  wholesale_price: zohoItem.rate,
  wholesale_only: zohoItem.item_type === 'wholesale_only'
});
```

### Customers Migration

#### Loyverse → NutriPro Customers (Retail)
```typescript
interface LoyverseCustomer {
  customer_id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  total_spent: number;
  visits: number;
}

interface NutriProCustomer {
  id: uuid;
  customer_number: string;
  customer_type: 'retail';
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  address_line1?: string;
  is_active: boolean;
}

const migrateRetailCustomer = (loyverseCustomer: LoyverseCustomer): NutriProCustomer => {
  const [firstName, ...lastNameParts] = loyverseCustomer.name.split(' ');
  return {
    id: generateUUID(),
    customer_number: `RET-${loyverseCustomer.customer_id}`,
    customer_type: 'retail',
    first_name: firstName,
    last_name: lastNameParts.join(' ') || '',
    email: loyverseCustomer.email,
    phone: standardizePhone(loyverseCustomer.phone),
    address_line1: loyverseCustomer.address,
    is_active: true
  };
};
```

#### Zoho Books → NutriPro Customers (Wholesale)
```typescript
interface ZohoContact {
  contact_id: string;
  contact_name: string;
  company_name: string;
  email: string;
  phone: string;
  billing_address: ZohoAddress;
  credit_limit: number;
  payment_terms: number;
}

const migrateWholesaleCustomer = (zohoContact: ZohoContact): NutriProCustomer => ({
  id: generateUUID(),
  customer_number: `WHO-${zohoContact.contact_id}`,
  customer_type: 'wholesale',
  company_name: zohoContact.company_name,
  email: zohoContact.email,
  phone: standardizePhone(zohoContact.phone),
  address_line1: zohoContact.billing_address.street,
  city: zohoContact.billing_address.city,
  state: zohoContact.billing_address.state,
  postal_code: zohoContact.billing_address.zip,
  credit_limit: zohoContact.credit_limit,
  payment_terms: zohoContact.payment_terms,
  is_active: true
});
```

### Transactions Migration

#### Loyverse → NutriPro Transactions
```typescript
interface LoyverseReceipt {
  receipt_id: string;
  customer_id?: string;
  employee_id: string;
  total_money: number;
  tax_money: number;
  discount_money: number;
  created_at: string;
  payment_type: string;
  line_items: LoyverseLineItem[];
}

const migrateTransaction = (receipt: LoyverseReceipt): NutriProTransaction => ({
  id: generateUUID(),
  transaction_number: `LV-${receipt.receipt_id}`,
  customer_id: mapCustomerId(receipt.customer_id),
  staff_id: mapStaffId(receipt.employee_id),
  transaction_type: 'sale',
  status: 'completed',
  subtotal: receipt.total_money - receipt.tax_money,
  tax_amount: receipt.tax_money,
  discount_amount: receipt.discount_money,
  total_amount: receipt.total_money,
  payment_method: mapPaymentMethod(receipt.payment_type),
  created_at: new Date(receipt.created_at),
  synced_at: new Date() // Mark as already synced
});
```

## Migration Scripts

### Database Migration Script Structure
```sql
-- 1. Create temporary staging tables
CREATE TABLE temp_loyverse_products AS SELECT * FROM loyverse_export;
CREATE TABLE temp_zoho_contacts AS SELECT * FROM zoho_export;

-- 2. Data cleansing and transformation
UPDATE temp_loyverse_products 
SET item_name = TRIM(REGEXP_REPLACE(item_name, '\s+', ' ', 'g'));

-- 3. Insert into production tables with conflict resolution
INSERT INTO products (id, sku, name, retail_price, cost_price, stock_quantity, is_active)
SELECT 
  gen_random_uuid(),
  COALESCE(barcode, 'LV-' || item_id),
  item_name,
  price,
  COALESCE(cost, 0),
  COALESCE(current_stock, 0),
  true
FROM temp_loyverse_products
ON CONFLICT (sku) DO UPDATE SET
  name = EXCLUDED.name,
  retail_price = EXCLUDED.retail_price,
  updated_at = now();

-- 4. Validation queries
SELECT COUNT(*) as migrated_products FROM products WHERE sku LIKE 'LV-%';
SELECT COUNT(*) as missing_prices FROM products WHERE retail_price IS NULL;

-- 5. Clean up temporary tables
DROP TABLE temp_loyverse_products;
DROP TABLE temp_zoho_contacts;
```

### Migration Validation Checklist

#### Data Integrity Checks
- [ ] **Record Counts**: Verify all records migrated
- [ ] **Referential Integrity**: All foreign keys valid
- [ ] **Data Types**: All fields have correct data types
- [ ] **Constraints**: All database constraints satisfied
- [ ] **Duplicates**: No unexpected duplicate records

#### Business Logic Validation
- [ ] **Pricing**: All products have valid prices
- [ ] **Inventory**: Stock levels are reasonable
- [ ] **Customers**: Contact information is valid
- [ ] **Transactions**: Financial totals are accurate
- [ ] **Relationships**: Customer-transaction relationships intact

#### Application Testing
- [ ] **Admin Panel**: All features work with migrated data
- [ ] **POS App**: Can process transactions normally
- [ ] **Wholesale Portal**: Customers can log in and place orders
- [ ] **Reports**: Generate accurate reports
- [ ] **Search**: All search functions work correctly

## Rollback Plan

### Rollback Triggers
- Data corruption detected
- Critical application failures
- Unacceptable performance degradation
- User acceptance test failures

### Rollback Procedure
1. **Stop all applications**
2. **Restore database from pre-migration backup**
3. **Restart applications with old configuration**
4. **Verify system functionality**
5. **Communicate status to stakeholders**

### Post-Rollback Actions
- Analyze failure causes
- Fix migration scripts
- Schedule new migration attempt
- Update documentation

## Go-Live Checklist

### Pre-Go-Live (24 hours before)
- [ ] Final data export from source systems
- [ ] Complete migration in staging environment
- [ ] User acceptance testing completed
- [ ] Staff training completed
- [ ] Rollback plan tested and ready

### Go-Live Day
- [ ] System maintenance window scheduled
- [ ] Migration scripts executed successfully
- [ ] Data validation completed
- [ ] Application testing passed
- [ ] Users notified of system availability
- [ ] Monitor system performance

### Post-Go-Live (First week)
- [ ] Daily data integrity checks
- [ ] User feedback collection
- [ ] Performance monitoring
- [ ] Issue resolution tracking
- [ ] Documentation updates

---

*Document Version: 1.0*  
*Last Updated: 2025-07-07*  
*Next Review: Upon completion of development setup documentation*
