# NutriPro Final Requirements Summary

## Project Overview - Complete Specification

Based on comprehensive discussions, NutriPro is a sophisticated, AI-powered Point of Sale and business management platform designed specifically for Nutricenter's supplement retail and wholesale operations.

## Core Business Requirements

### **Current Situation**
- **Business**: Nutricenter - Dietary supplements and health products
- **Current Systems**: Loyverse (retail) + Zoho Books (wholesale) - to be replaced
- **Scale**: 15 daily transactions, <10 staff, 10+ wholesale clients
- **Data**: 4 years of historical sales data available
- **Growth Plans**: 2nd location, franchise opportunities, e-commerce expansion

### **Key Business Challenges Solved**
1. **Unified Inventory**: Single system for retail and wholesale operations
2. **Coach Program Management**: Sophisticated referral system with credits and commissions
3. **Vendor Management**: Streamlined purchasing with 10+ international suppliers
4. **Multi-Currency Operations**: AWG local sales with international vendor purchasing
5. **AI-Powered Insights**: Inventory forecasting and customer behavior analysis

## Detailed Feature Specifications

### **1. Product Management System**

#### Master Products with Variants
- **Master Product**: Base product information (e.g., "Whey Protein")
- **Variant Toggle**: Enable/disable variants for products
- **Variant Types**: Configurable (Flavor, Size, Strength, etc.)
- **Variant Options**: Specific values (Chocolate, Vanilla, Small, Large)
- **Individual SKUs**: Each variant has unique SKU and barcode
- **Shared Base Info**: Description, ingredients, images inherited from master

#### Pricing Structure (Multi-Currency)
- **Retail Price**: Customer price in AWG (Aruban Florins)
- **Purchase Price**: Vendor cost in vendor's preferred currency
- **Landing Cost**: Total cost including shipping in AWG
- **Wholesale Price**: B2B client price in AWG
- **Currency Auto-Detection**: System shows vendor currency when brand is selected

#### Product Information Fields
- Brand (linked to vendor)
- Product name
- SKU (unique per variant)
- Barcode (unique per variant)
- Notes
- Multiple images
- Wholesale availability toggle
- Stock quantities and minimum levels

### **2. Coach Referral Program**

#### Coach Management
- **Current Scale**: 4 coaches, expanding post-launch
- **Registration**: Complete coach profiles with certifications
- **Client Assignment**: Retail customers assigned to specific coaches
- **Performance Tracking**: Coach effectiveness analytics

#### Credit System
- **Monthly Credits**: Manually set amount per coach (non-rollover)
- **Auto-Reset**: Credits reset to set amount each month
- **Expiration**: Unused credits expire at month-end
- **No Alerts**: No low-credit notifications needed

#### Referral Fees
- **Individual Rates**: Each coach has custom percentage rate
- **Immediate Processing**: Fees calculated when client purchases
- **No Tiers**: Flat percentage structure
- **Real-time Tracking**: Instant commission calculations

### **3. Vendor & Brand Management**

#### Vendor Database
- **Current Scale**: 10+ vendors, all registered in system
- **Contact Information**: Complete vendor profiles
- **Business Terms**: Payment terms, credit limits, minimum orders
- **Currency Preference**: Each vendor has preferred currency
- **Communication**: Email integration for order placement

#### Brand Organization
- **Brand-to-Vendor**: Each brand assigned to specific vendor
- **Auto-Currency**: Brand selection auto-shows vendor currency
- **Product Linking**: Products linked to brands for vendor identification

### **4. Advanced Purchasing System**

#### Purchase Order Management
- **Order Creation**: Complete PO lifecycle management
- **Vendor Communication**: Automated email integration
- **Multi-Currency**: Orders in vendor's preferred currency
- **AWG Conversion**: Automatic conversion for reporting
- **No Approval Workflow**: Managers can place orders directly

#### Order Processing
- **Frequency**: Order-based on stock performance (not scheduled)
- **Minimum Orders**: Vendor-specific minimum amounts enforced
- **Receiving Workflow**: Track deliveries and discrepancies
- **Performance Tracking**: Vendor reliability scoring

### **5. Multi-Currency System**

#### Currency Structure
- **Base Currency**: AWG (Aruban Florin) for all local operations
- **Vendor Currencies**: USD, EUR, GBP, CAD, etc.
- **Exchange Rates**: Manual entry with historical tracking
- **Auto-Conversion**: Real-time conversion for reporting

#### Implementation
- **Sales & Taxes**: All in AWG
- **Purchase Orders**: In vendor's preferred currency
- **Reporting**: Unified AWG reporting with currency breakdowns
- **Exchange Rate Functions**: Built-in conversion utilities

### **6. AI-Powered Features (Priority)**

#### Inventory Forecasting (High Priority)
- **Seasonal Patterns**: Q1 sports nutrition surge recognition
- **4-Year Data**: Historical analysis for accurate predictions
- **Demand Prediction**: 30-day forecasting for top products
- **Smart Reordering**: AI suggestions with human approval required

#### Customer Insights (High Priority)
- **Behavior Analysis**: Purchase pattern recognition
- **Coach Performance**: Effectiveness analytics and optimization
- **Churn Prevention**: At-risk customer identification
- **Lifetime Value**: Customer value predictions

#### Business Intelligence
- **Sales Trends**: Pattern recognition with actionable insights
- **Exception Reporting**: Automated alerts for unusual patterns
- **Performance Analytics**: Staff and coach performance tracking
- **Pricing Optimization**: Future phase consideration

### **7. User Roles & Permissions**

#### Three-Tier Access System
- **Admin**: Full system access and configuration
- **Staff**: Operational access, can view but not edit sensitive data
- **Accountant**: Financial reporting and analysis access

#### Permission Structure
- **Staff Access**: Can see vendor info and coach commissions (read-only)
- **Coach Access**: No system access, receive reports/statements only
- **Data Security**: Role-based access control throughout system

### **8. Mobile Dashboard Requirements**

#### Top 5 Mobile Metrics
1. **Daily Sales Total**: Real-time revenue tracking
2. **Inventory Alerts**: Low stock and critical notifications
3. **Vendor Order Status**: Purchase order tracking
4. **Cash Flow**: Outstanding payments and financial status
5. **System Health**: Overall operational status

#### Mobile Optimization
- **Responsive Design**: Optimized for phone and tablet access
- **Real-time Updates**: Live data synchronization
- **Offline Capability**: Critical functions work without internet

## Technical Architecture Summary

### **Technology Stack**
- **Monorepo**: Turborepo for unified development
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Real-time)
- **Web Apps**: Next.js 14 (Admin Panel, Wholesale Portal)
- **Mobile**: React Native + Expo (POS Tablet)
- **Deployment**: Netlify (cost-effective choice)
- **AI/ML**: TensorFlow.js + Custom analytics engine

### **Database Design**
- **Multi-Currency Support**: Currency tables with exchange rates
- **Product Variants**: Master-variant relationship structure
- **Coach Program**: Credits, referrals, and performance tracking
- **Vendor Management**: Complete supplier relationship management
- **AI Data**: Forecasting and analytics data structures

### **Key Integrations**
- **Offline POS**: SQLite local storage with sync
- **Email Automation**: Vendor communication integration
- **Currency Conversion**: Real-time exchange rate handling
- **AI Processing**: Machine learning pipeline integration

## Implementation Timeline

### **Phase 1: Enhanced MVP (5 months)**
- **Month 1**: Database design, core architecture, admin panel foundation
- **Month 2**: Product management with variants, vendor/brand system
- **Month 3**: Coach program, purchasing system, multi-currency
- **Month 4**: POS tablet app with offline sync
- **Month 5**: Wholesale portal, data migration, basic AI features

### **Phase 2: AI Enhancement (3 months)**
- **Month 6**: Advanced inventory forecasting
- **Month 7**: Customer behavior analytics and coach optimization
- **Month 8**: Business intelligence and automated insights

### **Phase 3: Expansion (Future)**
- **Sales Agent App**: Field sales management
- **Delivery App**: Route optimization and tracking
- **E-commerce Integration**: WordPress/WooCommerce plugin
- **Franchise Platform**: Multi-location management

## Success Metrics & ROI

### **Expected Benefits**
- **Cost Savings**: $500+/month (software + inventory optimization)
- **Revenue Increase**: 15-25% through AI insights and optimization
- **Efficiency Gains**: 60% reduction in manual tasks
- **Inventory Optimization**: 20-30% reduction in carrying costs
- **Coach Program ROI**: Measurable increase in customer retention

### **Technical Success Criteria**
- **System Uptime**: >99.5% availability
- **Response Times**: <2 seconds for all operations
- **Forecast Accuracy**: >85% for weekly demand predictions
- **Data Migration**: Zero data loss from existing systems
- **User Adoption**: <30 minutes training time for staff

## Risk Mitigation

### **High-Priority Risks**
1. **Multi-Currency Complexity**: Comprehensive testing of exchange rate calculations
2. **Data Migration**: Phased approach with extensive validation
3. **AI Accuracy**: Conservative implementation with human oversight
4. **Offline Sync**: Robust conflict resolution and retry mechanisms

### **Mitigation Strategies**
- **Extensive Testing**: Unit, integration, and user acceptance testing
- **Phased Rollout**: Gradual implementation with fallback options
- **User Training**: Comprehensive staff education program
- **Continuous Monitoring**: Real-time system health and performance tracking

---

This comprehensive specification represents a sophisticated, enterprise-level platform that will transform Nutricenter's operations and provide a strong foundation for future growth and franchise opportunities.

*Document Version: 1.0*  
*Last Updated: 2025-07-07*  
*Status: Ready for Development*
