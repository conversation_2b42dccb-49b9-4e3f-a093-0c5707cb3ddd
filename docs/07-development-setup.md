# NutriPro Development Setup & Deployment Guide

## Development Environment Setup

### Prerequisites (macOS)

#### Required Software
```bash
# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Node.js (LTS version)
brew install node

# Install pnpm (package manager)
npm install -g pnpm

# Install Git
brew install git

# Install Docker Desktop
brew install --cask docker

# Install VS Code
brew install --cask visual-studio-code
```

#### Mobile Development Setup
```bash
# Install Expo CLI
npm install -g @expo/cli

# Install EAS CLI (for builds)
npm install -g eas-cli

# Install Netlify CLI (for deployment)
npm install -g netlify-cli

# Install Android Studio (for Android development)
brew install --cask android-studio

# Install Xcode (for iOS development - App Store)
# Download from Mac App Store

# Install iOS Simulator
xcode-select --install
```

#### Database Tools
```bash
# Install PostgreSQL client
brew install postgresql

# Install Supabase CLI
npm install -g supabase

# Install database GUI (optional)
brew install --cask dbeaver-community
```

### Project Setup

#### 1. Clone Repository
```bash
git clone https://github.com/your-org/nutripro.git
cd nutripro
```

#### 2. Install Dependencies
```bash
# Install all dependencies using Turborepo
pnpm install

# Verify Turborepo setup
pnpm turbo --version
```

#### 3. Environment Configuration

Create environment files for each application:

**Root `.env.local`**:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/nutripro

# Development
NODE_ENV=development
```

**Admin Panel `.env.local`**:
```env
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

**Wholesale Portal `.env.local`**:
```env
NEXT_PUBLIC_APP_URL=http://localhost:3001
NEXT_PUBLIC_API_URL=http://localhost:3001/api
```

**Mobile Apps `.env`**:
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_API_URL=http://localhost:3000/api
EXPO_PUBLIC_AI_API_URL=http://localhost:8000/api
```

**AI Backend `.env`**:
```env
DATABASE_URL=postgresql://user:password@localhost:5432/nutripro
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
GOOGLE_GEMINI_API_KEY=your_gemini_api_key
ENVIRONMENT=development
```

#### 4. Database Setup

```bash
# Start local Supabase instance
supabase start

# Run database migrations
supabase db reset

# Seed development data
pnpm run db:seed
```

#### 5. AI Backend Setup

```bash
# Navigate to AI backend
cd apps/ai-backend

# Create Python virtual environment
python -m venv venv
source venv/bin/activate  # On macOS/Linux

# Install Python dependencies
pip install -r requirements.txt

# Set up Google Gemini API key
# Get API key from https://ai.google.dev/
export GOOGLE_GEMINI_API_KEY=your_api_key_here
```

#### 6. Start Development Servers

```bash
# Start all applications
pnpm dev

# Or start individual applications
pnpm dev --filter=admin-panel
pnpm dev --filter=wholesale-portal
pnpm dev --filter=pos-tablet

# Start AI backend separately
cd apps/ai-backend
uvicorn main:app --reload --port 8000
```

### Development Workflow

#### Branch Strategy
```
main (production)
├── develop (integration)
├── feature/admin-dashboard
├── feature/pos-offline-sync
├── feature/wholesale-portal
└── hotfix/critical-bug-fix
```

#### Commit Convention
```bash
# Format: type(scope): description
git commit -m "feat(admin): add product management dashboard"
git commit -m "fix(pos): resolve offline sync issue"
git commit -m "docs(api): update authentication endpoints"
```

#### Code Quality Checks
```bash
# Run linting
pnpm lint

# Run type checking
pnpm type-check

# Run tests
pnpm test

# Run all checks
pnpm check-all
```

## Testing Strategy

### Unit Testing
```bash
# Run unit tests
pnpm test:unit

# Run with coverage
pnpm test:coverage

# Watch mode
pnpm test:watch
```

### Integration Testing
```bash
# Run API integration tests
pnpm test:integration

# Run database tests
pnpm test:db
```

### End-to-End Testing
```bash
# Run E2E tests (web)
pnpm test:e2e

# Run mobile E2E tests
pnpm test:e2e:mobile
```

### Testing Configuration

**Jest Configuration (`jest.config.js`)**:
```javascript
module.exports = {
  projects: [
    '<rootDir>/apps/admin-panel',
    '<rootDir>/apps/wholesale-portal',
    '<rootDir>/packages/*'
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

## CI/CD Pipeline

### GitHub Actions Workflow

**`.github/workflows/ci.yml`**:
```yaml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run linting
        run: pnpm lint
      
      - name: Run type checking
        run: pnpm type-check
      
      - name: Run tests
        run: pnpm test:ci
      
      - name: Build applications
        run: pnpm build

  deploy-staging:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Deploy to staging
        run: echo "Deploy to staging environment"

  deploy-production:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: echo "Deploy to production environment"
```

### Mobile App CI/CD

**`.github/workflows/mobile.yml`**:
```yaml
name: Mobile CI/CD

on:
  push:
    paths:
      - 'apps/pos-tablet/**'
      - 'apps/sales-agent-app/**'
      - 'apps/delivery-app/**'

jobs:
  build-android:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          expo-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build Android APK
        run: eas build --platform android --non-interactive
```

## Deployment Strategy

### Environment Structure
```
Production Environment
├── Web Apps (Netlify)
│   ├── admin-panel.nutripro.com
│   └── wholesale.nutripro.com
├── Mobile Apps (Expo/Play Store)
│   ├── NutriPro POS (Internal)
│   ├── NutriPro Sales (Internal)
│   └── NutriPro Delivery (Internal)
├── Database (Supabase)
│   └── Production PostgreSQL
└── Storage (Supabase)
    └── Product images, receipts

Staging Environment
├── Web Apps (Netlify Preview)
│   ├── admin-staging.nutripro.com
│   └── wholesale-staging.nutripro.com
├── Mobile Apps (Expo Development)
│   └── Development builds
└── Database (Supabase)
    └── Staging PostgreSQL
```

### Web Application Deployment

#### Netlify Configuration (`netlify.toml`)
```toml
[build]
  base = "/"
  command = "pnpm build"
  publish = "apps/admin-panel/out"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--prefix=/dev/null"

[[redirects]]
  from = "/admin/*"
  to = "/admin/:splat"
  status = 200

[[redirects]]
  from = "/wholesale/*"
  to = "/wholesale/:splat"
  status = 200

# Admin Panel Site
[context.admin-panel]
  base = "apps/admin-panel"
  command = "pnpm build"
  publish = "out"

# Wholesale Portal Site
[context.wholesale-portal]
  base = "apps/wholesale-portal"
  command = "pnpm build"
  publish = "out"
```

#### Deployment Commands
```bash
# Deploy to staging
netlify deploy --dir=apps/admin-panel/out

# Deploy to production
netlify deploy --prod --dir=apps/admin-panel/out

# Deploy specific app
netlify deploy --prod --dir=apps/wholesale-portal/out
```

### Mobile Application Deployment

#### EAS Configuration (`eas.json`)
```json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "distribution": "internal",
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "distribution": "store"
    }
  },
  "submit": {
    "production": {
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "internal"
      }
    }
  }
}
```

#### Mobile Deployment Commands
```bash
# Build development version
eas build --profile development --platform android

# Build production version
eas build --profile production --platform android

# Submit to Play Store (internal testing)
eas submit --platform android
```

### Database Deployment

#### Migration Strategy
```bash
# Run migrations on staging
supabase db push --db-url $STAGING_DATABASE_URL

# Run migrations on production
supabase db push --db-url $PRODUCTION_DATABASE_URL

# Create migration
supabase migration new add_new_feature

# Reset database (development only)
supabase db reset
```

### Monitoring and Logging

#### Application Monitoring
```bash
# Install Sentry for error tracking
pnpm add @sentry/nextjs @sentry/react-native

# Configure Sentry
# sentry.client.config.js
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
});
```

#### Performance Monitoring
- **Web Vitals**: Built-in Next.js analytics
- **Database Performance**: Supabase dashboard
- **API Response Times**: Custom middleware logging
- **Mobile Performance**: Expo analytics

### Security Considerations

#### Environment Variables
```bash
# Never commit these to version control
SUPABASE_SERVICE_ROLE_KEY=
DATABASE_PASSWORD=
JWT_SECRET=
STRIPE_SECRET_KEY=

# Use Vercel/Expo secrets management
vercel env add SUPABASE_SERVICE_ROLE_KEY
eas secret:create --name SUPABASE_SERVICE_ROLE_KEY --value your_key
```

#### Security Headers
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  }
];

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
};
```

### Backup and Recovery

#### Database Backups
```bash
# Automated daily backups (Supabase handles this)
# Manual backup
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d).sql

# Restore from backup
psql $DATABASE_URL < backup_20250707.sql
```

#### Application Backups
```bash
# Backup environment configurations
cp .env.local .env.backup.$(date +%Y%m%d)

# Backup custom configurations
tar -czf config_backup_$(date +%Y%m%d).tar.gz \
  vercel.json eas.json package.json
```

### Maintenance Procedures

#### Regular Maintenance Tasks
```bash
# Weekly dependency updates
pnpm update --interactive

# Monthly security audit
pnpm audit

# Database maintenance
supabase db vacuum
supabase db analyze

# Log rotation and cleanup
find ./logs -name "*.log" -mtime +30 -delete
```

#### Performance Optimization
```bash
# Bundle analysis
pnpm analyze

# Database query optimization
EXPLAIN ANALYZE SELECT * FROM products WHERE stock_quantity < min_stock_level;

# Image optimization
pnpm optimize-images
```

### Troubleshooting Guide

#### Common Issues

**Build Failures**:
```bash
# Clear cache and reinstall
rm -rf node_modules .next
pnpm install
pnpm build
```

**Database Connection Issues**:
```bash
# Check Supabase status
supabase status

# Test connection
psql $DATABASE_URL -c "SELECT version();"
```

**Mobile Build Issues**:
```bash
# Clear Expo cache
expo r -c

# Reset Metro cache
npx react-native start --reset-cache
```

#### Debug Commands
```bash
# Enable debug logging
DEBUG=* pnpm dev

# Database query logging
SUPABASE_DEBUG=true pnpm dev

# Mobile debugging
expo start --dev-client --clear
```

### Documentation Maintenance

#### Keep Documentation Updated
- [ ] Update API documentation after endpoint changes
- [ ] Update deployment guides after infrastructure changes
- [ ] Update troubleshooting guide with new issues
- [ ] Review and update security procedures quarterly

#### Documentation Tools
```bash
# Generate API documentation
pnpm docs:api

# Generate component documentation
pnpm docs:components

# Update changelog
pnpm changelog
```

---

*Document Version: 1.0*
*Last Updated: 2025-07-07*
*Next Review: Upon project initialization*
