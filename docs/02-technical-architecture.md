# NutriPro Technical Architecture

## Architecture Overview

NutriPro follows a modern, cloud-first architecture with offline-capable mobile clients. The system is designed as a monorepo with shared components and utilities across all applications.

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │  Tablet POS App │    │ Wholesale Portal│
│   (Next.js)     │    │ (React Native)  │    │   (Next.js)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Supabase      │
                    │   - Database    │
                    │   - Auth        │
                    │   - Storage     │
                    │   - Real-time   │
                    └─────────────────┘
```

## Technology Stack

### Core Technologies
- **Monorepo**: Turborepo + pnpm
- **Backend**: Supabase (PostgreSQL + Auth + Storage + Real-time)
- **AI/ML Backend**: Python FastAPI + Google Gemini 2.5 Flash
- **Web Framework**: Next.js 14+ (App Router)
- **Mobile Framework**: React Native with Expo
- **Language**: TypeScript throughout
- **Web UI**: shadcn/ui + Tailwind CSS
- **Mobile UI**: Gluestack UI (formerly NativeBase)
- **State Management**: Zustand + TanStack Query

### Development Tools
- **Package Manager**: pnpm
- **Code Quality**: ESLint + Prettier + <PERSON>sky
- **Testing**: Jest + React Testing Library + Detox (E2E)
- **CI/CD**: GitHub Actions
- **Deployment**: Netlify (web) + EAS Build (mobile)
- **AI/ML**: Python FastAPI + Google Gemini API + TensorFlow (optional)

## Monorepo Structure

```
nutripro/
├── apps/
│   ├── admin-panel/          # Next.js admin dashboard (shadcn/ui)
│   ├── pos-tablet/           # React Native POS app (Gluestack UI)
│   ├── wholesale-portal/     # Next.js wholesale client portal (shadcn/ui)
│   ├── ai-backend/           # Python FastAPI + Gemini 2.5 Flash
│   ├── sales-agent-app/      # React Native (Phase 2)
│   ├── delivery-app/         # React Native (Phase 2)
│   └── wordpress-plugin/     # PHP plugin (Phase 2)
├── packages/
│   ├── ui-web/               # shadcn/ui components for web
│   ├── ui-mobile/            # Gluestack UI components for mobile
│   ├── database/             # Supabase client & types
│   ├── shared/               # Shared utilities & types
│   ├── offline-sync/         # Offline synchronization logic
│   ├── ai-client/            # AI backend client
│   └── config/               # Shared configurations
├── docs/                     # Project documentation
├── scripts/                  # Build and deployment scripts
└── tools/                    # Development tools
```

## Database Architecture (Supabase/PostgreSQL)

### Core Tables

#### Products
```sql
products (
  id: uuid PRIMARY KEY,
  sku: varchar UNIQUE NOT NULL,
  name: varchar NOT NULL,
  description: text,
  category_id: uuid REFERENCES categories(id),
  retail_price: decimal(10,2),
  wholesale_price: decimal(10,2),
  cost_price: decimal(10,2),
  stock_quantity: integer DEFAULT 0,
  min_stock_level: integer DEFAULT 0,
  barcode: varchar,
  is_active: boolean DEFAULT true,
  created_at: timestamptz DEFAULT now(),
  updated_at: timestamptz DEFAULT now()
)
```

#### Customers
```sql
customers (
  id: uuid PRIMARY KEY,
  customer_type: enum('retail', 'wholesale'),
  name: varchar NOT NULL,
  email: varchar,
  phone: varchar,
  address: jsonb,
  wholesale_discount: decimal(5,2), -- percentage
  credit_limit: decimal(10,2),
  payment_terms: integer, -- days
  is_active: boolean DEFAULT true,
  created_at: timestamptz DEFAULT now(),
  updated_at: timestamptz DEFAULT now()
)
```

#### Transactions
```sql
transactions (
  id: uuid PRIMARY KEY,
  transaction_number: varchar UNIQUE NOT NULL,
  customer_id: uuid REFERENCES customers(id),
  staff_id: uuid REFERENCES auth.users(id),
  transaction_type: enum('sale', 'return', 'wholesale_order'),
  status: enum('pending', 'completed', 'cancelled', 'refunded'),
  subtotal: decimal(10,2) NOT NULL,
  tax_amount: decimal(10,2) DEFAULT 0,
  discount_amount: decimal(10,2) DEFAULT 0,
  total_amount: decimal(10,2) NOT NULL,
  payment_method: varchar,
  notes: text,
  synced_at: timestamptz, -- for offline sync tracking
  created_at: timestamptz DEFAULT now(),
  updated_at: timestamptz DEFAULT now()
)
```

#### Transaction Items
```sql
transaction_items (
  id: uuid PRIMARY KEY,
  transaction_id: uuid REFERENCES transactions(id) ON DELETE CASCADE,
  product_id: uuid REFERENCES products(id),
  quantity: integer NOT NULL,
  unit_price: decimal(10,2) NOT NULL,
  discount_amount: decimal(10,2) DEFAULT 0,
  line_total: decimal(10,2) NOT NULL,
  created_at: timestamptz DEFAULT now()
)
```

### Offline Sync Tables

#### Sync Queue
```sql
sync_queue (
  id: uuid PRIMARY KEY,
  device_id: varchar NOT NULL,
  table_name: varchar NOT NULL,
  record_id: uuid NOT NULL,
  operation: enum('insert', 'update', 'delete'),
  data: jsonb,
  created_at: timestamptz DEFAULT now(),
  synced_at: timestamptz,
  retry_count: integer DEFAULT 0
)
```

#### Device Registry
```sql
devices (
  id: uuid PRIMARY KEY,
  device_name: varchar NOT NULL,
  device_type: enum('pos_tablet', 'sales_tablet', 'delivery_mobile'),
  last_sync: timestamptz,
  is_active: boolean DEFAULT true,
  created_at: timestamptz DEFAULT now()
)
```

## Application Architecture

### Admin Panel (Next.js)
**Purpose**: Central management dashboard for store operations

**Key Features**:
- Product catalog management
- Inventory tracking and adjustments
- Customer management (retail + wholesale)
- Sales reporting and analytics
- Staff management
- System configuration

**Architecture**:
- Next.js 14 with App Router
- Server-side rendering for SEO and performance
- Real-time updates via Supabase subscriptions
- Role-based access control

### POS Tablet App (React Native + Expo + Gluestack UI)
**Purpose**: Offline-first point-of-sale for in-store transactions

**Key Features**:
- Product search and barcode scanning
- Transaction processing with offline capability
- Customer lookup and management
- Payment method selection
- Receipt printing and email
- Real-time inventory updates
- Coach referral tracking

**Offline-First Strategy** (Critical Priority):
- **SQLite Local Database**: Complete product catalog, customers, transactions
- **Offline Transaction Processing**: Full POS functionality without internet
- **Smart Sync Manager**: Background sync every 30 seconds when online
- **Manual Sync Control**: Force sync button for immediate synchronization
- **Conflict Resolution**: Intelligent handling of concurrent edits
- **Queue Management**: Failed operations queued for retry with exponential backoff
- **Data Integrity**: Checksums and validation for sync operations

**Architecture**:
```
┌─────────────────┐
│   Gluestack UI  │
│   Components    │
├─────────────────┤
│   React Native  │
│   Business Logic│
├─────────────────┤
│   Zustand +     │
│   TanStack Query│
├─────────────────┤
│   Offline Sync  │
│   Manager       │
├─────────────────┤
│   SQLite        │
│   Local DB      │
└─────────────────┘
```

**Offline Data Strategy**:
- **Products**: Full catalog with variants, pricing, stock levels
- **Customers**: Complete customer database with coach assignments
- **Transactions**: Local transaction storage with sync queue
- **Settings**: Payment methods, tax rates, store configuration
- **Sync Status**: Track what needs synchronization

### AI Backend (Python FastAPI + Google Gemini 2.5 Flash)
**Purpose**: Advanced analytics and machine learning services

**Key Features**:
- Inventory demand forecasting using Gemini 2.5 Flash
- Customer behavior analysis and insights
- Smart reorder recommendations
- Seasonal pattern recognition
- Coach performance optimization
- Natural language query processing

**Architecture**:
```
┌─────────────────┐
│   FastAPI       │
│   REST API      │
├─────────────────┤
│   Google Gemini │
│   2.5 Flash     │
├─────────────────┤
│   Data Pipeline │
│   & Processing  │
├─────────────────┤
│   Supabase      │
│   Integration   │
└─────────────────┘
```

**AI Services**:
- **Demand Forecasting**: Gemini-powered prediction models
- **Customer Insights**: Behavior analysis and segmentation
- **Inventory Optimization**: Smart reorder point calculations
- **Seasonal Analysis**: Pattern recognition for supplement sales
- **Coach Analytics**: Performance metrics and optimization

### Wholesale Portal (Next.js + shadcn/ui)
**Purpose**: Self-service portal for wholesale clients

**Key Features**:
- Product catalog with wholesale pricing
- Order placement and history
- Account management
- Invoice downloads
- Real-time inventory availability
- AI-powered product recommendations

**Architecture**:
- Next.js with static generation for product pages
- shadcn/ui for consistent design system
- Authentication via Supabase Auth
- Real-time inventory updates
- PDF generation for invoices
- AI recommendations from Python backend

## Offline Synchronization Strategy

### Data Flow
1. **Online Mode**: Direct API calls to Supabase
2. **Offline Mode**: Store operations in local SQLite
3. **Sync Process**: Background service syncs local changes

### Sync Algorithm
```typescript
interface SyncOperation {
  id: string;
  table: string;
  operation: 'insert' | 'update' | 'delete';
  data: any;
  timestamp: number;
  deviceId: string;
}

class OfflineSyncManager {
  async syncToServer(): Promise<void> {
    const pendingOps = await this.getLocalPendingOperations();
    
    for (const op of pendingOps) {
      try {
        await this.executeServerOperation(op);
        await this.markOperationSynced(op.id);
      } catch (error) {
        await this.handleSyncError(op, error);
      }
    }
  }
  
  async syncFromServer(): Promise<void> {
    const lastSync = await this.getLastSyncTimestamp();
    const serverChanges = await this.fetchServerChanges(lastSync);
    
    for (const change of serverChanges) {
      await this.applyLocalChange(change);
    }
    
    await this.updateLastSyncTimestamp();
  }
}
```

### Conflict Resolution
- **Last Write Wins**: For simple data updates
- **Merge Strategy**: For complex objects (customer info)
- **Manual Resolution**: For critical conflicts (inventory adjustments)

## Security Architecture

### Authentication & Authorization
- **Supabase Auth**: JWT-based authentication
- **Role-Based Access Control (RBAC)**:
  - `admin`: Full system access
  - `manager`: Store operations + reporting
  - `staff`: POS operations only
  - `wholesale_client`: Portal access only

### Data Security
- **Encryption**: All data encrypted at rest and in transit
- **API Security**: Row Level Security (RLS) policies
- **Local Storage**: Encrypted SQLite database on mobile
- **Audit Trail**: All operations logged with user attribution

### Network Security
- **HTTPS Only**: All communications encrypted
- **API Rate Limiting**: Prevent abuse
- **CORS Configuration**: Restrict cross-origin requests
- **Input Validation**: Sanitize all user inputs

## Performance Considerations

### Database Optimization
- **Indexing Strategy**: Optimize for common queries
- **Connection Pooling**: Efficient database connections
- **Query Optimization**: Use Supabase query builder efficiently
- **Caching**: Redis for frequently accessed data

### Mobile Performance
- **Bundle Optimization**: Code splitting and lazy loading
- **Image Optimization**: Compressed images with caching
- **Memory Management**: Efficient state management
- **Battery Optimization**: Minimize background processing

### Web Performance
- **Static Generation**: Pre-render pages where possible
- **Image Optimization**: Next.js Image component
- **Code Splitting**: Automatic route-based splitting
- **CDN**: Vercel Edge Network for global distribution

## Deployment Architecture

### Production Environment
- **Web Apps**: Vercel (auto-scaling, global CDN)
- **Mobile Apps**: Expo Application Services (EAS)
- **Database**: Supabase (managed PostgreSQL)
- **File Storage**: Supabase Storage
- **Monitoring**: Sentry for error tracking

### Development Workflow
- **Local Development**: Docker Compose for Supabase local
- **Staging Environment**: Preview deployments on Vercel
- **CI/CD Pipeline**: GitHub Actions
- **Testing**: Automated testing on all PRs

## API Design

### REST API Endpoints

#### Products API
```
GET    /api/products              # List products with filters
POST   /api/products              # Create new product
GET    /api/products/:id          # Get product details
PUT    /api/products/:id          # Update product
DELETE /api/products/:id          # Soft delete product
POST   /api/products/:id/adjust   # Adjust inventory
```

#### Transactions API
```
GET    /api/transactions          # List transactions
POST   /api/transactions          # Create transaction
GET    /api/transactions/:id      # Get transaction details
PUT    /api/transactions/:id      # Update transaction
POST   /api/transactions/sync     # Bulk sync from offline
```

#### Sync API
```
POST   /api/sync/push             # Push offline changes
GET    /api/sync/pull             # Pull server changes
GET    /api/sync/status           # Get sync status
```

### Real-time Subscriptions
- **Inventory Updates**: Real-time stock level changes
- **New Orders**: Wholesale portal notifications
- **System Alerts**: Low stock, sync issues
- **User Activity**: Multi-user coordination

## Development Guidelines

### Code Standards
- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration with custom rules
- **Prettier**: Consistent code formatting
- **Commit Convention**: Conventional Commits specification

### Testing Strategy
- **Unit Tests**: 80%+ coverage for business logic
- **Integration Tests**: API endpoints and database operations
- **E2E Tests**: Critical user journeys
- **Performance Tests**: Load testing for peak usage

### Error Handling
- **Graceful Degradation**: System continues with reduced functionality
- **User-Friendly Messages**: Clear error communication
- **Logging**: Comprehensive error tracking
- **Recovery**: Automatic retry mechanisms

---

*Document Version: 1.0*
*Last Updated: 2025-07-07*
*Next Review: Upon completion of MVP specifications*
