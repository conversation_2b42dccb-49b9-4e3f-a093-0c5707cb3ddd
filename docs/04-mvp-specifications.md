# NutriPro MVP Phase Specifications

## MVP Overview

The MVP (Minimum Viable Product) phase focuses on replacing the current Loyverse + Zoho Books setup with a unified platform that handles both retail and wholesale operations.

### MVP Goals
1. **Replace Loyverse**: Complete POS functionality for in-store sales
2. **Replace Zoho Books**: Wholesale order management and invoicing
3. **Unify Operations**: Single platform for inventory, customers, and reporting
4. **Enable Growth**: Foundation for Phase 2 expansion

### Success Criteria
- [ ] 100% of retail transactions processed through new POS
- [ ] 80% of wholesale orders placed through online portal
- [ ] Zero data loss during migration
- [ ] Staff can operate system with <30 minutes training
- [ ] System handles offline POS operations reliably

## Application 1: Admin Panel (Next.js)

### Purpose
Central management dashboard for store operations, inventory, and business analytics.

### Target Users
- **Store Manager** (primary user)
- **Admin Staff** (secondary user)

### Core Features

#### 1. Dashboard & Analytics
**User Story**: As a store manager, I want to see key business metrics at a glance so I can make informed decisions.

**Features**:
- Daily/weekly/monthly sales summary
- Top-selling products
- Low stock alerts
- Recent transactions
- Wholesale order status
- Staff performance metrics

**Acceptance Criteria**:
- [ ] Dashboard loads in <3 seconds
- [ ] Real-time updates for critical metrics
- [ ] Responsive design for tablet/desktop
- [ ] Export reports to PDF/Excel
- [ ] Date range filtering for all metrics

#### 2. Product Management
**User Story**: As a store manager, I want to manage my product catalog efficiently so I can maintain accurate inventory and pricing.

**Features**:
- Add/edit/delete products
- Bulk product import/export
- Category management
- Pricing management (retail/wholesale)
- Barcode generation and printing
- Product image management
- Inventory adjustments
- Stock movement history

**Acceptance Criteria**:
- [ ] Bulk import from CSV with validation
- [ ] Barcode scanning for quick product lookup
- [ ] Automatic low stock notifications
- [ ] Price change history tracking
- [ ] Image upload with compression
- [ ] Duplicate SKU prevention

#### 3. Customer Management
**User Story**: As a store manager, I want to manage both retail and wholesale customers in one place.

**Features**:
- Customer database (retail + wholesale)
- Customer profiles with purchase history
- Wholesale client pricing tiers
- Credit limit management
- Customer communication log
- Loyalty program management

**Acceptance Criteria**:
- [ ] Search customers by name/phone/email
- [ ] Merge duplicate customer records
- [ ] Wholesale pricing automatically applied
- [ ] Credit limit warnings
- [ ] Customer purchase analytics

#### 4. Transaction Management
**User Story**: As a store manager, I want to view and manage all transactions across retail and wholesale channels.

**Features**:
- Transaction history and search
- Refund and return processing
- Transaction details and receipts
- Payment method tracking
- Sales analytics and reporting

**Acceptance Criteria**:
- [ ] Search transactions by multiple criteria
- [ ] Process partial refunds
- [ ] Reprint receipts
- [ ] Export transaction data
- [ ] Real-time transaction sync from POS

#### 5. Inventory Management
**User Story**: As a store manager, I want complete visibility and control over inventory levels.

**Features**:
- Real-time stock levels
- Inventory adjustments
- Stock movement tracking
- Reorder point management
- Supplier management
- Purchase order creation

**Acceptance Criteria**:
- [ ] Real-time stock updates across all channels
- [ ] Automated reorder alerts
- [ ] Stock adjustment audit trail
- [ ] Low stock email notifications
- [ ] Inventory valuation reports

#### 6. Coach Program Management
**User Story**: As a store manager, I want to manage the coach referral program efficiently.

**Features**:
- Coach registration and profile management
- Client assignment to coaches
- Credit system management (initial credits, usage tracking)
- Referral fee calculation and tracking
- Coach performance analytics
- Commission payment processing

**Acceptance Criteria**:
- [ ] Coach registration with certification tracking
- [ ] Automatic client assignment workflow
- [ ] Real-time credit balance tracking
- [ ] Automatic referral fee calculations
- [ ] Coach performance dashboards
- [ ] Commission payment reports

#### 7. Vendor & Brand Management
**User Story**: As a store manager, I want to manage my suppliers and product brands systematically.

**Features**:
- Vendor database with contact information
- Brand management under vendors
- Vendor performance tracking
- Communication preferences management
- Supplier reliability scoring

**Acceptance Criteria**:
- [ ] Complete vendor profile management
- [ ] Brand-to-vendor relationship tracking
- [ ] Vendor performance metrics
- [ ] Communication method preferences
- [ ] Supplier reliability scoring system

#### 8. Purchasing System
**User Story**: As a store manager, I want to streamline my purchasing process with vendors.

**Features**:
- Purchase order creation and management
- Automated vendor communication
- Order tracking and receiving
- Budget and approval workflows
- Vendor performance analytics

**Acceptance Criteria**:
- [ ] PO generation with automatic numbering
- [ ] Email integration for vendor communication
- [ ] Receiving workflow with discrepancy handling
- [ ] Approval workflow for large orders
- [ ] Vendor delivery performance tracking

#### 9. Wholesale Management
**User Story**: As a store manager, I want to efficiently manage wholesale operations and client relationships.

**Features**:
- Wholesale order management
- Invoice generation and tracking
- Payment tracking
- Wholesale pricing management
- Client account management

**Acceptance Criteria**:
- [ ] Automatic invoice generation
- [ ] Payment status tracking
- [ ] Wholesale price tier management
- [ ] Credit limit enforcement
- [ ] Overdue payment alerts

#### 10. AI-Powered Analytics (Basic)
**User Story**: As a store manager, I want intelligent insights to make better business decisions.

**Features**:
- Sales trend analysis with insights
- Basic demand forecasting
- Low stock predictions
- Customer behavior insights
- Automated business alerts

**Acceptance Criteria**:
- [ ] Weekly sales trend reports with insights
- [ ] 30-day demand forecasting for top products
- [ ] Predictive low stock alerts
- [ ] Customer purchase pattern analysis
- [ ] Automated exception reporting

### Technical Specifications

#### Technology Stack
- **Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: Zustand + TanStack Query
- **Authentication**: Supabase Auth
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel

#### Key Components
```typescript
// Core layout components
AdminLayout
Sidebar
Header
Breadcrumbs

// Dashboard components
MetricsCard
SalesChart
RecentTransactions
LowStockAlert

// Product management
ProductList
ProductForm
BulkImport
BarcodeGenerator

// Customer management
CustomerList
CustomerForm
CustomerHistory

// Transaction management
TransactionList
TransactionDetails
RefundForm
```

#### API Routes
```
/api/dashboard/metrics
/api/products
/api/customers
/api/transactions
/api/inventory
/api/wholesale
/api/reports

// New features
/api/coaches
/api/coach-transactions
/api/vendors
/api/brands
/api/purchase-orders
/api/forecasting
/api/ai/insights
/api/ai/recommendations
```

## Application 2: POS Tablet App (React Native + Expo)

### Purpose
Offline-capable point-of-sale application for in-store retail transactions.

### Target Users
- **Store Staff** (cashiers, sales associates)

### Core Features

#### 1. Product Search & Selection
**User Story**: As a cashier, I want to quickly find and add products to a sale.

**Features**:
- Barcode scanning
- Product search by name/SKU
- Category browsing
- Quick access to frequently sold items
- Product details display

**Acceptance Criteria**:
- [ ] Barcode scan adds product in <2 seconds
- [ ] Search results appear as user types
- [ ] Product images display correctly
- [ ] Out-of-stock products clearly marked
- [ ] Price displays prominently

#### 2. Transaction Processing
**User Story**: As a cashier, I want to process sales quickly and accurately.

**Features**:
- Add/remove items from cart
- Quantity adjustments
- Apply discounts
- Calculate tax automatically
- Multiple payment methods
- Split payments
- Receipt generation

**Acceptance Criteria**:
- [ ] Cart updates instantly
- [ ] Tax calculated automatically
- [ ] Payment processing <10 seconds
- [ ] Receipt prints immediately
- [ ] Transaction saved locally if offline

#### 3. Customer Management
**User Story**: As a cashier, I want to quickly look up customer information for sales.

**Features**:
- Customer search
- Customer creation
- Purchase history
- Loyalty program integration
- Customer preferences

**Acceptance Criteria**:
- [ ] Customer search by phone/email
- [ ] Quick customer creation form
- [ ] Purchase history loads <3 seconds
- [ ] Loyalty points calculated automatically

#### 4. Offline Operation
**User Story**: As a cashier, I need the POS to work even when internet is down.

**Features**:
- Local SQLite database
- Offline transaction processing
- Background sync when online
- Manual sync option
- Conflict resolution

**Acceptance Criteria**:
- [ ] All core functions work offline
- [ ] Sync status clearly displayed
- [ ] Failed syncs retry automatically
- [ ] Manual sync completes <30 seconds
- [ ] No data loss during sync

#### 5. Reporting & End-of-Day
**User Story**: As a cashier, I want to close out my shift accurately.

**Features**:
- Daily sales summary
- Payment method breakdown
- Cash drawer reconciliation
- Shift reports
- Transaction export

**Acceptance Criteria**:
- [ ] Accurate sales totals
- [ ] Payment method totals match
- [ ] Export data for accounting
- [ ] Shift handover documentation

### Technical Specifications

#### Technology Stack
- **Framework**: React Native with Expo
- **Navigation**: Expo Router
- **State Management**: Zustand + TanStack Query
- **Local Database**: SQLite (expo-sqlite)
- **Barcode Scanning**: expo-barcode-scanner
- **Printing**: expo-print
- **Offline Sync**: Custom sync manager

#### Key Screens
```typescript
// Main screens
ProductSearch
Cart
Checkout
CustomerLookup
Settings

// Modal screens
ProductDetails
CustomerForm
PaymentMethod
Receipt
SyncStatus
```

#### Offline Sync Strategy
```typescript
interface SyncManager {
  // Queue operations for sync
  queueOperation(operation: SyncOperation): void;
  
  // Sync with server
  syncToServer(): Promise<SyncResult>;
  syncFromServer(): Promise<SyncResult>;
  
  // Handle conflicts
  resolveConflicts(conflicts: Conflict[]): Promise<void>;
  
  // Status monitoring
  getSyncStatus(): SyncStatus;
}
```

## Application 3: Wholesale Portal (Next.js)

### Purpose
Self-service portal for wholesale clients to place orders and manage their accounts.

### Target Users
- **Wholesale Clients** (business customers)

### Core Features

#### 1. Product Catalog
**User Story**: As a wholesale client, I want to browse products with my specific pricing.

**Features**:
- Product catalog with wholesale pricing
- Category filtering
- Product search
- Bulk pricing tiers
- Product availability status
- Detailed product information

**Acceptance Criteria**:
- [ ] Only wholesale-available products shown
- [ ] Client-specific pricing displayed
- [ ] Real-time stock availability
- [ ] Product images and descriptions
- [ ] Minimum order quantities enforced

#### 2. Order Management
**User Story**: As a wholesale client, I want to easily place and track orders.

**Features**:
- Shopping cart functionality
- Quick reorder from history
- Order templates/favorites
- Order tracking
- Order history
- Invoice downloads

**Acceptance Criteria**:
- [ ] Cart persists between sessions
- [ ] One-click reorder functionality
- [ ] Order status updates in real-time
- [ ] PDF invoice generation
- [ ] Order confirmation emails

#### 3. Account Management
**User Story**: As a wholesale client, I want to manage my account information and view my account status.

**Features**:
- Account profile management
- Credit limit visibility
- Payment history
- Outstanding invoices
- Account statements
- Contact information updates

**Acceptance Criteria**:
- [ ] Real-time credit limit display
- [ ] Payment history with receipts
- [ ] Overdue invoice notifications
- [ ] Profile updates save immediately
- [ ] Account statements downloadable

#### 4. Communication
**User Story**: As a wholesale client, I want to communicate with the store about my orders.

**Features**:
- Order notes and special instructions
- Message center
- Support ticket system
- Order inquiry forms

**Acceptance Criteria**:
- [ ] Messages delivered to store staff
- [ ] Order notes visible to fulfillment team
- [ ] Response time tracking
- [ ] Email notifications for updates

### Technical Specifications

#### Technology Stack
- **Framework**: Next.js 14 (App Router)
- **Styling**: Tailwind CSS + shadcn/ui
- **Authentication**: Supabase Auth (wholesale client accounts)
- **State Management**: Zustand + TanStack Query
- **PDF Generation**: jsPDF or Puppeteer
- **Email**: Supabase Edge Functions + Resend

#### Key Pages
```typescript
// Public pages
Login
ForgotPassword

// Authenticated pages
Dashboard
ProductCatalog
ProductDetails
Cart
Checkout
OrderHistory
OrderDetails
AccountProfile
InvoiceHistory
Messages
```

#### Authentication Flow
```typescript
// Wholesale client authentication
interface WholesaleAuth {
  login(email: string, password: string): Promise<AuthResult>;
  resetPassword(email: string): Promise<void>;
  updateProfile(profile: WholesaleProfile): Promise<void>;
  getCurrentClient(): WholesaleClient | null;
}
```

---

*Document Version: 1.0*  
*Last Updated: 2025-07-07*  
*Next Review: Upon completion of Phase 2 specifications*
