name: Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

env:
  NODE_VERSION: "18"
  PYTHON_VERSION: "3.11"

jobs:
  # Job 1: Create Release
  create-release:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      release_id: ${{ steps.create_release.outputs.id }}
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Generate changelog
        id: changelog
        run: |
          # Generate changelog from git commits
          echo "CHANGELOG<<EOF" >> $GITHUB_OUTPUT
          git log --pretty=format:"- %s (%h)" $(git describe --tags --abbrev=0 HEAD^)..HEAD >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name || github.event.inputs.version }}
          release_name: NutriPro ${{ github.ref_name || github.event.inputs.version }}
          body: |
            ## What's Changed
            ${{ steps.changelog.outputs.CHANGELOG }}
            
            ## Deployment
            - ✅ Admin Panel: Deployed to production
            - ✅ Wholesale Portal: Deployed to production  
            - ✅ AI Backend: Deployed to production
            - ✅ Mobile Apps: Available for internal testing
            
            ## Installation
            See [Development Setup](./docs/07-development-setup.md) for installation instructions.
          draft: false
          prerelease: false

  # Job 2: Build and Package Applications
  build-release:
    name: Build Release Artifacts
    runs-on: ubuntu-latest
    needs: create-release
    strategy:
      matrix:
        app: [admin-panel, wholesale-portal]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build ${{ matrix.app }}
        run: pnpm build --filter=${{ matrix.app }}
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.PROD_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.PROD_SUPABASE_ANON_KEY }}

      - name: Package ${{ matrix.app }}
        run: |
          cd apps/${{ matrix.app }}
          tar -czf ../../${{ matrix.app }}-${{ github.ref_name || github.event.inputs.version }}.tar.gz .next/ package.json

      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ./${{ matrix.app }}-${{ github.ref_name || github.event.inputs.version }}.tar.gz
          asset_name: ${{ matrix.app }}-${{ github.ref_name || github.event.inputs.version }}.tar.gz
          asset_content_type: application/gzip

  # Job 3: Build Mobile Apps for Release
  build-mobile-release:
    name: Build Mobile Release
    runs-on: ubuntu-latest
    needs: create-release
    strategy:
      matrix:
        app: [pos-tablet]
        platform: [android, ios]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Setup Expo CLI
        run: npm install -g @expo/cli eas-cli

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Authenticate with Expo
        run: expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Build ${{ matrix.app }} for ${{ matrix.platform }}
        run: |
          cd apps/${{ matrix.app }}
          eas build --platform ${{ matrix.platform }} --profile production --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

  # Job 4: Deploy Production Release
  deploy-release:
    name: Deploy Production Release
    runs-on: ubuntu-latest
    needs: [create-release, build-release]
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Deploy to Production
        run: |
          # Deploy admin panel
          pnpm build --filter=admin-panel
          npx netlify deploy --prod --dir=apps/admin-panel/out --site=${{ secrets.NETLIFY_ADMIN_SITE_ID }}
          
          # Deploy wholesale portal
          pnpm build --filter=wholesale-portal  
          npx netlify deploy --prod --dir=apps/wholesale-portal/out --site=${{ secrets.NETLIFY_WHOLESALE_SITE_ID }}
        env:
          NETLIFY_AUTH_TOKEN: ${{ secrets.NETLIFY_AUTH_TOKEN }}
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.PROD_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.PROD_SUPABASE_ANON_KEY }}

      - name: Run Database Migrations
        run: |
          npx supabase db push --db-url ${{ secrets.PROD_DATABASE_URL }}
        env:
          SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN }}

      - name: Notify Release Success
        uses: 8398a7/action-slack@v3
        with:
          status: success
          text: "🎉 NutriPro ${{ github.ref_name || github.event.inputs.version }} Released Successfully!"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Job 5: Update Documentation
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: deploy-release
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Update changelog
        run: |
          echo "## ${{ github.ref_name || github.event.inputs.version }} - $(date +%Y-%m-%d)" >> CHANGELOG.md
          echo "" >> CHANGELOG.md
          git log --pretty=format:"- %s (%h)" $(git describe --tags --abbrev=0 HEAD^)..HEAD >> CHANGELOG.md
          echo "" >> CHANGELOG.md
          echo "" >> CHANGELOG.md

      - name: Commit changelog
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add CHANGELOG.md
          git commit -m "docs: update changelog for ${{ github.ref_name || github.event.inputs.version }}" || exit 0
          git push
