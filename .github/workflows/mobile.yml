name: Mobile CI/CD

on:
  push:
    branches: [main, develop]
    paths:
      - 'apps/pos-tablet/**'
      - 'apps/sales-agent-app/**'
      - 'apps/delivery-app/**'
      - 'packages/**'
  pull_request:
    branches: [main, develop]
    paths:
      - 'apps/pos-tablet/**'
      - 'apps/sales-agent-app/**'
      - 'apps/delivery-app/**'
      - 'packages/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: "18"
  EXPO_CLI_VERSION: "latest"

jobs:
  # Job 1: Mobile Linting and Type Check
  mobile-quality:
    name: Mobile Code Quality
    runs-on: ubuntu-latest
    strategy:
      matrix:
        app: [pos-tablet, sales-agent-app, delivery-app]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Lint ${{ matrix.app }}
        run: pnpm lint --filter=${{ matrix.app }}

      - name: Type check ${{ matrix.app }}
        run: pnpm type-check --filter=${{ matrix.app }}

  # Job 2: Mobile Unit Tests
  mobile-test:
    name: Mobile Tests
    runs-on: ubuntu-latest
    needs: mobile-quality
    strategy:
      matrix:
        app: [pos-tablet, sales-agent-app, delivery-app]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests for ${{ matrix.app }}
        run: pnpm test:ci --filter=${{ matrix.app }}

  # Job 3: EAS Build (Development)
  eas-build-dev:
    name: EAS Development Build
    runs-on: ubuntu-latest
    needs: [mobile-quality, mobile-test]
    if: github.ref == 'refs/heads/develop'
    strategy:
      matrix:
        app: [pos-tablet]
        platform: [android]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: Setup EAS CLI
        run: npm install -g eas-cli

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Authenticate with Expo
        run: expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Build ${{ matrix.app }} for ${{ matrix.platform }} (development)
        run: |
          cd apps/${{ matrix.app }}
          eas build --platform ${{ matrix.platform }} --profile development --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

  # Job 4: EAS Build (Production)
  eas-build-prod:
    name: EAS Production Build
    runs-on: ubuntu-latest
    needs: [mobile-quality, mobile-test]
    if: github.ref == 'refs/heads/main'
    strategy:
      matrix:
        app: [pos-tablet]
        platform: [android, ios]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Setup Expo CLI
        run: npm install -g @expo/cli@${{ env.EXPO_CLI_VERSION }}

      - name: Setup EAS CLI
        run: npm install -g eas-cli

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Authenticate with Expo
        run: expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: Build ${{ matrix.app }} for ${{ matrix.platform }} (production)
        run: |
          cd apps/${{ matrix.app }}
          eas build --platform ${{ matrix.platform }} --profile production --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

  # Job 5: E2E Tests (Optional)
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [mobile-quality, mobile-test]
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: "8"

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run E2E tests
        run: pnpm test:e2e --filter=pos-tablet
        continue-on-error: true
