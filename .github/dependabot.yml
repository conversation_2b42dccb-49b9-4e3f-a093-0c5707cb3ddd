version: 2
updates:
  # Root package.json
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 5
    reviewers:
      - "ivin<PERSON>ekiman"
    assignees:
      - "i<PERSON><PERSON><PERSON>iman"
    commit-message:
      prefix: "deps"
      include: "scope"
    labels:
      - "dependencies"
      - "automated"

  # Admin Panel
  - package-ecosystem: "npm"
    directory: "/apps/admin-panel"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "ivinroekiman"
    commit-message:
      prefix: "deps(admin)"
    labels:
      - "dependencies"
      - "admin-panel"

  # Wholesale Portal
  - package-ecosystem: "npm"
    directory: "/apps/wholesale-portal"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "ivinroekiman"
    commit-message:
      prefix: "deps(wholesale)"
    labels:
      - "dependencies"
      - "wholesale-portal"

  # POS Tablet
  - package-ecosystem: "npm"
    directory: "/apps/pos-tablet"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "ivin<PERSON>ekiman"
    commit-message:
      prefix: "deps(pos)"
    labels:
      - "dependencies"
      - "pos-tablet"

  # AI Backend Python dependencies
  - package-ecosystem: "pip"
    directory: "/apps/ai-backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 3
    reviewers:
      - "ivinroekiman"
    commit-message:
      prefix: "deps(ai)"
    labels:
      - "dependencies"
      - "ai-backend"
      - "python"

  # Shared Packages - UI
  - package-ecosystem: "npm"
    directory: "/packages/ui"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 2
    reviewers:
      - "ivinroekiman"
    commit-message:
      prefix: "deps(ui)"
    labels:
      - "dependencies"
      - "shared-packages"

  # Shared Packages - Database
  - package-ecosystem: "npm"
    directory: "/packages/database"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 2
    reviewers:
      - "ivinroekiman"
    commit-message:
      prefix: "deps(database)"
    labels:
      - "dependencies"
      - "shared-packages"

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    open-pull-requests-limit: 2
    reviewers:
      - "ivinroekiman"
    commit-message:
      prefix: "ci"
    labels:
      - "github-actions"
      - "ci-cd"
