name: Bug Report
description: Report a bug in NutriPro
title: "[BUG] "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out the form below with as much detail as possible.

  - type: dropdown
    id: application
    attributes:
      label: Application
      description: Which application is affected?
      options:
        - Admin Panel
        - Wholesale Portal
        - POS Tablet
        - Sales Agent App
        - Delivery App
        - AI Backend
        - Shared Package
        - Other
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened.
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.
      placeholder: Drag and drop screenshots here...

  - type: dropdown
    id: severity
    attributes:
      label: Severity
      description: How severe is this bug?
      options:
        - Critical (System unusable)
        - High (Major feature broken)
        - Medium (Minor feature broken)
        - Low (Cosmetic issue)
    validations:
      required: true

  - type: input
    id: environment
    attributes:
      label: Environment
      description: Environment where the bug occurred
      placeholder: "Production / Staging / Development"
    validations:
      required: true

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
      placeholder: Any additional information...
