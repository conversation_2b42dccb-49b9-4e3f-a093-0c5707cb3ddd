# NutriPro CI/CD Pipeline Guide

This document explains the complete CI/CD setup for the NutriPro monorepo.

## 🏗️ Pipeline Overview

The CI/CD pipeline consists of 5 main workflows:

1. **CI/CD Pipeline** (`ci.yml`) - Main quality checks and builds
2. **Mobile CI/CD** (`mobile.yml`) - React Native app builds
3. **Deploy** (`deploy.yml`) - Automated deployments
4. **Security** (`security.yml`) - Security scans and compliance
5. **Release** (`release.yml`) - Release management

## 🔄 Workflow Triggers

### CI/CD Pipeline (`ci.yml`)
- **Triggers**: Push to `main`/`develop`, Pull Requests
- **Jobs**: Code quality, unit tests, build applications, Python tests
- **Duration**: ~8-12 minutes

### Mobile CI/CD (`mobile.yml`)
- **Triggers**: Changes to mobile apps or packages
- **Jobs**: Mobile linting, tests, EAS builds
- **Duration**: ~15-25 minutes (including builds)

### Deploy (`deploy.yml`)
- **Triggers**: Successful CI completion on `main`/`develop`
- **Jobs**: Deploy to staging/production, database backups
- **Duration**: ~5-10 minutes

### Security (`security.yml`)
- **Triggers**: Push, PR, daily schedule (2 AM UTC)
- **Jobs**: Dependency scan, secret scan, CodeQL, container scan
- **Duration**: ~10-15 minutes

### Release (`release.yml`)
- **Triggers**: Git tags (`v*.*.*`), manual dispatch
- **Jobs**: Create release, build artifacts, deploy production
- **Duration**: ~20-30 minutes

## 🎯 Quality Gates

### Code Quality Checks
- ✅ ESLint (all applications)
- ✅ Prettier formatting
- ✅ TypeScript type checking
- ✅ Security vulnerability scan
- ✅ License compliance check

### Testing Requirements
- ✅ Unit tests (>80% coverage)
- ✅ Integration tests
- ✅ E2E tests (mobile apps)
- ✅ Python tests (AI backend)

### Security Scans
- ✅ Dependency vulnerabilities
- ✅ Secret scanning (TruffleHog)
- ✅ Code analysis (CodeQL)
- ✅ Container security (Trivy)

## 🚀 Deployment Strategy

### Branch Strategy
```
main (production)
├── develop (staging)
├── feature/* (feature branches)
└── hotfix/* (emergency fixes)
```

### Environment Flow
```
Feature Branch → develop → staging → main → production
```

### Deployment Targets

#### Staging (develop branch)
- **Admin Panel**: `admin-staging.nutripro.com`
- **Wholesale Portal**: `wholesale-staging.nutripro.com`
- **AI Backend**: Railway staging environment
- **Mobile Apps**: Expo development builds

#### Production (main branch)
- **Admin Panel**: `admin.nutripro.com`
- **Wholesale Portal**: `wholesale.nutripro.com`
- **AI Backend**: Railway production environment
- **Mobile Apps**: App Store internal testing

## 📱 Mobile App Pipeline

### Build Profiles
- **Development**: Internal testing, development client
- **Preview**: Internal distribution, APK builds
- **Production**: App Store submission

### Platform Support
- **Android**: Google Play Console (internal testing)
- **iOS**: App Store Connect (TestFlight)

### Build Triggers
- **Development**: Push to `develop`
- **Production**: Push to `main` or release tags

## 🔒 Security Pipeline

### Daily Security Scans
- Dependency vulnerability checks
- Secret scanning across all files
- License compliance verification
- Container security analysis

### Security Notifications
- Slack alerts for security issues
- GitHub Security tab integration
- Automated issue creation for vulnerabilities

## 📊 Monitoring & Notifications

### Success Notifications
- ✅ Deployment success (Slack)
- ✅ Release completion (Slack)
- ✅ Build artifacts uploaded

### Failure Notifications
- ❌ Build failures (Slack)
- ❌ Security issues (Slack)
- ❌ Deployment failures (Slack)

### Monitoring Dashboards
- GitHub Actions dashboard
- Netlify deployment status
- Railway application metrics
- Expo build status

## 🛠️ Development Workflow

### 1. Feature Development
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat(admin): add new feature"

# Push and create PR
git push origin feature/new-feature
```

### 2. Pull Request Process
1. **Automated checks run** (CI pipeline)
2. **Code review required** (team member)
3. **All checks must pass** before merge
4. **Squash and merge** to develop

### 3. Staging Deployment
1. **Merge to develop** triggers staging deployment
2. **Test on staging** environment
3. **Validate functionality** before production

### 4. Production Release
1. **Merge develop to main** for production
2. **Create release tag** for versioning
3. **Automated production deployment**
4. **Post-deployment verification**

## 🔧 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check build logs
gh run list --workflow=ci.yml
gh run view [run-id]

# Local debugging
pnpm build --filter=admin-panel
pnpm type-check
pnpm lint
```

#### Deployment Issues
```bash
# Check deployment status
netlify status
railway status

# Manual deployment
netlify deploy --prod --dir=apps/admin-panel/out
```

#### Mobile Build Issues
```bash
# Check EAS build status
eas build:list

# Local debugging
cd apps/pos-tablet
expo doctor
eas build --platform android --local
```

### Emergency Procedures

#### Rollback Deployment
1. **Identify last good deployment**
2. **Revert to previous version**
3. **Notify team of rollback**
4. **Investigate and fix issue**

#### Security Incident
1. **Immediately revoke compromised secrets**
2. **Update GitHub secrets**
3. **Re-run security scans**
4. **Document incident**

## 📈 Performance Optimization

### Build Optimization
- **Turborepo caching** for faster builds
- **Parallel job execution** where possible
- **Artifact caching** for dependencies
- **Conditional builds** based on changes

### Cost Optimization
- **Efficient resource usage** in CI
- **Conditional deployments** to reduce costs
- **Optimized build matrices** for mobile apps
- **Smart caching strategies**

## 🔄 Maintenance

### Weekly Tasks
- Review failed builds and fix issues
- Update dependencies via Dependabot PRs
- Monitor security scan results
- Check deployment metrics

### Monthly Tasks
- Rotate secrets and tokens
- Review and update CI/CD configurations
- Analyze build performance metrics
- Update documentation

### Quarterly Tasks
- Security audit of entire pipeline
- Review and optimize build times
- Update CI/CD best practices
- Team training on new features

---

**📚 Additional Resources**
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Expo EAS Documentation](https://docs.expo.dev/eas/)
- [Netlify Deployment Guide](https://docs.netlify.com/)
- [Railway Deployment Guide](https://docs.railway.app/)
