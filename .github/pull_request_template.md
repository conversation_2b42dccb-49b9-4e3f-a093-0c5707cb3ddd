# Pull Request

## Description
Brief description of the changes in this PR.

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring
- [ ] CI/CD improvement

## Applications Affected
- [ ] Admin Panel
- [ ] Wholesale Portal
- [ ] POS Tablet
- [ ] Sales Agent App
- [ ] Delivery App
- [ ] AI Backend
- [ ] Shared Packages
- [ ] Documentation

## Changes Made
- 
- 
- 

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] E2E tests added/updated
- [ ] Manual testing completed
- [ ] All existing tests pass

### Test Instructions
1. 
2. 
3. 

## Screenshots (if applicable)
<!-- Add screenshots here -->

## Database Changes
- [ ] No database changes
- [ ] Schema changes (migrations included)
- [ ] Data migrations required
- [ ] New environment variables required

## Deployment Notes
- [ ] No special deployment steps required
- [ ] Requires environment variable updates
- [ ] Requires database migration
- [ ] Requires cache clearing
- [ ] Requires third-party service configuration

## Security Considerations
- [ ] No security implications
- [ ] Security review required
- [ ] New permissions/roles added
- [ ] Sensitive data handling changes

## Performance Impact
- [ ] No performance impact
- [ ] Performance improvement
- [ ] Potential performance impact (explain below)

## Breaking Changes
- [ ] No breaking changes
- [ ] Breaking changes (list below)

### Breaking Changes Details
<!-- If there are breaking changes, describe them here -->

## Related Issues
Closes #
Related to #

## Checklist
- [ ] Code follows the project's coding standards
- [ ] Self-review of code completed
- [ ] Code is properly commented
- [ ] Tests added/updated and passing
- [ ] Documentation updated (if needed)
- [ ] No console errors or warnings
- [ ] Responsive design tested (for UI changes)
- [ ] Accessibility considerations addressed
- [ ] Performance impact assessed

## Additional Notes
<!-- Any additional information that reviewers should know -->
