# GitHub Secrets Configuration

This document lists all the GitHub secrets required for the CI/CD pipeline to function properly.

## Repository Secrets

### Supabase Configuration
```
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_ACCESS_TOKEN=your_access_token

# Staging Environment
STAGING_SUPABASE_URL=https://your-staging-project.supabase.co
STAGING_SUPABASE_ANON_KEY=your_staging_anon_key

# Production Environment
PROD_SUPABASE_URL=https://your-prod-project.supabase.co
PROD_SUPABASE_ANON_KEY=your_prod_anon_key
PROD_DATABASE_URL=postgresql://user:pass@host:port/db
```

### Deployment Secrets
```
# Netlify
NETLIFY_AUTH_TOKEN=your_netlify_auth_token
NETLIFY_ADMIN_SITE_ID=your_admin_site_id
NETLIFY_WHOLESALE_SITE_ID=your_wholesale_site_id

# Railway (AI Backend)
RAILWAY_TOKEN=your_railway_token
RAILWAY_PROJECT_ID=your_railway_project_id

# Application URLs
STAGING_ADMIN_URL=https://admin-staging.nutripro.com
STAGING_WHOLESALE_URL=https://wholesale-staging.nutripro.com
PROD_ADMIN_URL=https://admin.nutripro.com
PROD_WHOLESALE_URL=https://wholesale.nutripro.com
```

### Mobile App Secrets
```
# Expo
EXPO_TOKEN=your_expo_access_token

# App Store Connect (iOS)
APPLE_ID=<EMAIL>
ASC_APP_ID=your_app_store_connect_app_id
APPLE_TEAM_ID=your_apple_team_id

# Google Play Console (Android)
GOOGLE_SERVICE_ACCOUNT_KEY=your_google_service_account_json
```

### Security & Monitoring
```
# Code Security
SNYK_TOKEN=your_snyk_token
CODECOV_TOKEN=your_codecov_token

# Notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
```

### Backup & Storage
```
# AWS S3 (for backups)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
BACKUP_BUCKET=nutripro-backups

# Database
DB_PASSWORD=your_database_password
```

## Environment-Specific Secrets

### Staging Environment
- All secrets prefixed with `STAGING_`
- Used for develop branch deployments
- Safe for testing and validation

### Production Environment  
- All secrets prefixed with `PROD_`
- Used for main branch deployments
- Critical for live operations

## Security Best Practices

### Secret Management
1. **Never commit secrets** to the repository
2. **Use environment-specific secrets** for different deployment stages
3. **Rotate secrets regularly** (quarterly recommended)
4. **Use least privilege access** for all service accounts
5. **Monitor secret usage** through audit logs

### Access Control
1. **Limit repository access** to essential team members
2. **Use branch protection rules** to prevent direct pushes to main
3. **Require pull request reviews** for all changes
4. **Enable secret scanning** to detect accidental commits

### Monitoring
1. **Set up alerts** for failed deployments
2. **Monitor secret expiration** dates
3. **Track unusual access patterns**
4. **Regular security audits**

## Setup Instructions

### 1. Repository Settings
1. Go to your GitHub repository
2. Navigate to Settings → Secrets and variables → Actions
3. Add each secret listed above

### 2. Environment Setup
1. Create "staging" and "production" environments
2. Add environment-specific secrets
3. Configure environment protection rules

### 3. Branch Protection
1. Protect main and develop branches
2. Require status checks to pass
3. Require pull request reviews
4. Restrict pushes to specific users

### 4. Notifications
1. Set up Slack webhook for deployment notifications
2. Configure email alerts for security issues
3. Set up monitoring dashboards

## Troubleshooting

### Common Issues
1. **Secret not found**: Check secret name spelling and case sensitivity
2. **Permission denied**: Verify service account permissions
3. **Expired tokens**: Check token expiration dates
4. **Wrong environment**: Ensure correct environment-specific secrets

### Validation
```bash
# Test Supabase connection
curl -H "apikey: $SUPABASE_ANON_KEY" "$SUPABASE_URL/rest/v1/"

# Test Netlify authentication
netlify auth:status

# Test Expo authentication
expo whoami
```

## Security Incident Response

### If Secrets Are Compromised
1. **Immediately revoke** the compromised secrets
2. **Generate new secrets** for affected services
3. **Update GitHub secrets** with new values
4. **Audit access logs** for unauthorized usage
5. **Notify team members** of the incident
6. **Document the incident** for future reference

### Emergency Contacts
- Repository Admin: [Your contact info]
- Security Team: [Security contact]
- DevOps Lead: [DevOps contact]

---

**⚠️ Important**: This file should be kept up to date as new secrets are added or existing ones are modified. Regular audits should be performed to ensure all secrets are current and secure.
