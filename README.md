# NutriPro - Complete Point of Sale and Business Management Platform

A world-class, enterprise-level platform for supplement retail and wholesale operations with sophisticated features including AI-powered business intelligence, multi-currency operations, and offline-first POS system.

## 🏗️ Architecture

This is a **Turborepo monorepo** containing:

### Applications
- **Admin Panel** (`apps/admin-panel`) - Next.js 14 with shadcn/ui
- **Wholesale Portal** (`apps/wholesale-portal`) - Next.js 14 customer self-service
- **POS Tablet** (`apps/pos-tablet`) - React Native + Expo with offline-first architecture
- **Sales Agent App** (`apps/sales-agent-app`) - React Native mobile app
- **Delivery App** (`apps/delivery-app`) - React Native delivery tracking
- **AI Backend** (`apps/ai-backend`) - Python FastAPI + Google Gemini 2.5 Flash

### Shared Packages
- **UI Components** (`packages/ui`) - Shared React components
- **Database** (`packages/database`) - Supabase client and queries
- **Utils** (`packages/utils`) - Shared utilities and helpers
- **Types** (`packages/types`) - TypeScript type definitions
- **Auth** (`packages/auth`) - Authentication utilities
- **Config** (`packages/config`) - Shared configuration

### Tools
- **ESLint Config** (`tools/eslint-config`) - Shared linting rules
- **TypeScript Config** (`tools/tsconfig`) - Shared TypeScript configurations

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- pnpm 8+
- Python 3.11+
- Docker Desktop
- Supabase CLI

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd nutripro

# Install dependencies
pnpm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start Supabase locally
supabase start

# Run database migrations
supabase db reset

# Start all applications
pnpm dev
```

### Individual Application Development

```bash
# Admin Panel (http://localhost:3000)
pnpm dev --filter=admin-panel

# Wholesale Portal (http://localhost:3001)
pnpm dev --filter=wholesale-portal

# POS Tablet (Expo)
pnpm dev --filter=pos-tablet

# AI Backend (http://localhost:8000)
cd apps/ai-backend
python -m uvicorn main:app --reload
```

## 🎯 Key Features

### Core Business Features
- ✅ **Multi-Currency Support** (AWG base + vendor currencies)
- ✅ **FIFO Batch Management** (automatic oldest-first selection)
- ✅ **Expiry Tracking** (alerts and POS warnings)
- ✅ **Two-Tier Membership** (Regular 5% forever, Premium 10% annual)
- ✅ **Loyalty Points** (never expire, membership multipliers)
- ✅ **Coach Referral Program** (individual rates, automatic calculation)
- ✅ **Inventory Reservations** (wholesale quote integration)

### Technical Features
- ✅ **Offline-First POS** (Critical requirement!)
- ✅ **Real-time Synchronization**
- ✅ **AI-Powered Forecasting**
- ✅ **Multi-tenant Architecture**
- ✅ **Type-Safe Development**
- ✅ **Comprehensive Testing**

## 📱 Applications Overview

### Admin Panel
Complete business management dashboard with:
- Product management with variants and batch tracking
- Customer management with loyalty and membership
- Coach program management and performance analytics
- Vendor and brand management
- Purchase order system with multi-currency
- AI-powered dashboard with insights and recommendations
- Financial reporting and analytics

### POS Tablet App
Offline-first point of sale with:
- Product search with barcode scanning
- FIFO batch selection (automatic oldest-first)
- Expiry warnings for near-expiry products
- Loyalty points and membership discount application
- Coach referral tracking
- Receipt printing and email
- Real-time sync when online

### Wholesale Portal
Customer self-service portal with:
- Product catalog with wholesale pricing
- Order placement with automatic inventory reservation
- Account management and credit tracking
- Invoice downloads and payment history
- AI-powered product recommendations

### AI Backend
Google Gemini 2.5 Flash integration for:
- Demand forecasting algorithms
- Customer behavior analysis
- Inventory optimization recommendations
- Business intelligence and reporting

## 🛠️ Development

### Available Scripts

```bash
# Development
pnpm dev              # Start all applications
pnpm build            # Build all applications
pnpm lint             # Lint all code
pnpm type-check       # TypeScript checking
pnpm test             # Run all tests

# Database
pnpm db:generate      # Generate database types
pnpm db:push          # Push schema changes
pnpm db:seed          # Seed development data
pnpm db:studio        # Open Supabase Studio

# Mobile
pnpm mobile:start     # Start Expo development
pnpm mobile:build     # Build mobile apps
pnpm mobile:submit    # Submit to app stores

# Quality
pnpm check-all        # Run all quality checks
pnpm format           # Format all code
```

### Code Quality

This project uses:
- **TypeScript** with strict mode
- **ESLint** with custom configurations
- **Prettier** for code formatting
- **Husky** for git hooks
- **Commitlint** for conventional commits
- **Jest** for unit testing
- **Playwright** for E2E testing

### Git Workflow

```bash
# Commit format
git commit -m "feat(admin): add product management dashboard"
git commit -m "fix(pos): resolve offline sync issue"
git commit -m "docs(api): update authentication endpoints"
```

## 🗄️ Database Schema

The platform uses **PostgreSQL via Supabase** with:
- **50+ tables** covering all business operations
- **Row Level Security** for data protection
- **Real-time subscriptions** for live updates
- **FIFO batch tracking** with automatic selection
- **Multi-currency support** with exchange rates
- **Comprehensive audit trails**

Key tables include:
- Products with variants and batch tracking
- Customers with loyalty and membership
- Transactions with offline sync support
- Coach program with credit system
- Inventory with FIFO management
- Purchase orders with multi-currency

## 🤖 AI Integration

### Google Gemini 2.5 Flash Features
- **Inventory Forecasting**: Predict demand using historical data
- **Expiry Alerts**: Smart alerts for products nearing expiration
- **Customer Insights**: Behavior analysis and churn prediction
- **Seasonal Analysis**: Q1 sports nutrition surge recognition

### AI Capabilities
- Natural language query processing
- Pattern recognition for seasonal trends
- Recommendation engine for products
- Automated reorder suggestions

## 🚀 Deployment

### Production Environment
- **Web Apps**: Netlify hosting
- **Mobile Apps**: Expo EAS + Play Store
- **Database**: Supabase Pro
- **AI Backend**: Railway/Render
- **Storage**: Supabase Storage

### Estimated Monthly Costs: ~$125-155
- Netlify Pro: ~$19
- Supabase Pro: ~$25
- Railway/Render: ~$30
- Expo EAS: ~$29
- Google Gemini API: ~$20-50

## 📊 Business Impact

This platform provides:
- **60% reduction** in manual tasks
- **Zero stockouts** through AI forecasting
- **Automated commission** calculations
- **Increased customer retention** through loyalty program
- **Multi-currency operations** for global vendors
- **Offline reliability** for uninterrupted sales

## 📚 Documentation

- [Development Setup](./docs/07-development-setup.md)
- [Database Schema](./docs/03-database-schema.md)
- [Technical Architecture](./docs/02-technical-architecture.md)
- [MVP Specifications](./docs/04-mvp-specifications.md)
- [AI Features Integration](./docs/08-ai-features-integration.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run quality checks: `pnpm check-all`
5. Submit a pull request

## 📄 License

This project is proprietary and confidential.

---

**Built with ❤️ for NutriCenter - The future of supplement retail management**
