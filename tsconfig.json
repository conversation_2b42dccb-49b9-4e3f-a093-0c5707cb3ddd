{"extends": "./tools/tsconfig/base.json", "compilerOptions": {"baseUrl": ".", "paths": {"@nutripro/ui": ["./packages/ui/src"], "@nutripro/ui/*": ["./packages/ui/src/*"], "@nutripro/database": ["./packages/database/src"], "@nutripro/database/*": ["./packages/database/src/*"], "@nutripro/utils": ["./packages/utils/src"], "@nutripro/utils/*": ["./packages/utils/src/*"], "@nutripro/types": ["./packages/types/src"], "@nutripro/types/*": ["./packages/types/src/*"], "@nutripro/auth": ["./packages/auth/src"], "@nutripro/auth/*": ["./packages/auth/src/*"], "@nutripro/config": ["./packages/config/src"], "@nutripro/config/*": ["./packages/config/src/*"]}}, "include": ["apps/**/*", "packages/**/*", "tools/**/*"], "exclude": ["node_modules", "dist", "build", ".next", "coverage", "**/*.test.*", "**/*.spec.*"]}