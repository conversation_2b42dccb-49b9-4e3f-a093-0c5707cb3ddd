/** @type {import("eslint").Linter.Config} */
module.exports = {
  root: true,
  extends: ["@nutripro/eslint-config/base"],
  settings: {
    next: {
      rootDir: ["apps/*/"],
    },
  },
  overrides: [
    {
      files: ["apps/admin-panel/**/*", "apps/wholesale-portal/**/*"],
      extends: ["@nutripro/eslint-config/nextjs"],
    },
    {
      files: ["apps/pos-tablet/**/*", "apps/sales-agent-app/**/*", "apps/delivery-app/**/*"],
      extends: ["@nutripro/eslint-config/react-native"],
    },
    {
      files: ["apps/ai-backend/**/*"],
      extends: ["@nutripro/eslint-config/node"],
    },
  ],
};
