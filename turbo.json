{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local", "**/.env"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**", "build/**", "out/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^build"], "outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:unit": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:e2e": {"dependsOn": ["^build"], "outputs": ["coverage/**", "test-results/**"]}, "test:coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "test:watch": {"cache": false, "persistent": true}, "test:ci": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:seed": {"cache": false}, "db:studio": {"cache": false, "persistent": true}, "mobile:start": {"cache": false, "persistent": true}, "mobile:build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "mobile:submit": {"cache": false}, "ai:dev": {"cache": false, "persistent": true}, "ai:build": {"dependsOn": ["^build"], "outputs": ["dist/**"]}, "docs:api": {"dependsOn": ["^build"], "outputs": ["docs/api/**"]}, "docs:components": {"dependsOn": ["^build"], "outputs": ["docs/components/**"]}, "analyze": {"dependsOn": ["^build"], "outputs": ["analyze/**"]}}}