{"name": "@nutripro/config", "version": "0.0.0", "private": true, "description": "Shared configuration for NutriPro", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./env": "./src/env.ts", "./constants": "./src/constants.ts", "./database": "./src/database.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist"}, "dependencies": {"@nutripro/types": "workspace:*", "zod": "^3.22.4"}, "devDependencies": {"@nutripro/eslint-config": "workspace:*", "@nutripro/tsconfig": "workspace:*", "eslint": "^8.56.0", "typescript": "^5.3.3"}}