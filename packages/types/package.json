{"name": "@nutripro/types", "version": "0.0.0", "private": true, "description": "Shared TypeScript types for NutriPro", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./database": "./src/database.ts", "./api": "./src/api.ts", "./auth": "./src/auth.ts", "./pos": "./src/pos.ts", "./wholesale": "./src/wholesale.ts", "./coach": "./src/coach.ts", "./loyalty": "./src/loyalty.ts"}, "scripts": {"type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist"}, "devDependencies": {"@nutripro/eslint-config": "workspace:*", "@nutripro/tsconfig": "workspace:*", "eslint": "^8.56.0", "typescript": "^5.3.3"}}