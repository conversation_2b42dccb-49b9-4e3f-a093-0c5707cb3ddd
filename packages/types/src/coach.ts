import { UUID, Timestamp } from "./index";
import { Customer } from "./database";

// Coach program types
export interface Coach {
  id: UUID;
  coach_number: string;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country: string;
  certification_level?: string;
  certification_date?: string;
  specializations?: string[];
  monthly_credit_amount: number;
  current_credit_balance: number;
  last_credit_reset?: Timestamp;
  total_credits_earned: number;
  total_credits_used: number;
  referral_fee_percentage: number;
  referral_fee_flat: number;
  is_active: boolean;
  registration_date: Timestamp;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Coach transaction types
export type CoachTransactionType = "credit_issued" | "credit_used" | "referral_earned" | "referral_paid";

export interface CoachTransaction {
  id: UUID;
  coach_id: UUID;
  customer_id?: UUID;
  transaction_id?: UUID;
  transaction_type: CoachTransactionType;
  amount: number;
  description: string;
  credit_balance_before: number;
  credit_balance_after: number;
  created_at: Timestamp;
}

// Coach-customer relationship
export interface CoachCustomerAssignment {
  id: UUID;
  coach_id: UUID;
  customer_id: UUID;
  assigned_date: Timestamp;
  is_active: boolean;
  notes?: string;
}

// Coach performance analytics
export interface CoachPerformance {
  coach_id: UUID;
  period: {
    start_date: string;
    end_date: string;
  };
  metrics: {
    total_clients: number;
    active_clients: number;
    new_clients: number;
    total_referral_sales: number;
    total_referral_fees: number;
    average_client_value: number;
    client_retention_rate: number;
  };
  top_clients: {
    customer_id: UUID;
    customer_name: string;
    total_purchases: number;
    referral_fees_generated: number;
  }[];
  monthly_breakdown: {
    month: string;
    referral_sales: number;
    referral_fees: number;
    new_clients: number;
    credits_used: number;
  }[];
}

// Coach credit system
export interface CreditTransaction {
  id: UUID;
  coach_id: UUID;
  transaction_type: "monthly_reset" | "manual_adjustment" | "purchase_deduction";
  amount: number;
  balance_before: number;
  balance_after: number;
  description: string;
  reference_id?: UUID; // transaction_id for purchases
  created_by?: UUID; // staff member for manual adjustments
  created_at: Timestamp;
}

// Coach dashboard data
export interface CoachDashboard {
  coach: Coach;
  current_period: {
    credits_available: number;
    credits_used: number;
    referral_sales: number;
    referral_fees_earned: number;
    new_clients: number;
  };
  recent_transactions: CoachTransaction[];
  assigned_customers: (Customer & {
    assignment_date: Timestamp;
    total_purchases: number;
    last_purchase_date?: Timestamp;
  })[];
  upcoming_credit_reset: Timestamp;
}

// Coach program settings
export interface CoachProgramSettings {
  enabled: boolean;
  default_referral_percentage: number;
  default_monthly_credits: number;
  credit_reset_day: number; // day of month (1-31)
  minimum_purchase_for_referral: number;
  maximum_referral_percentage: number;
  auto_assign_new_customers: boolean;
  require_certification: boolean;
}

// Coach application/registration
export interface CoachApplication {
  id: UUID;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  certification_level?: string;
  certification_date?: string;
  specializations: string[];
  experience_years?: number;
  motivation: string;
  status: "pending" | "approved" | "rejected";
  reviewed_by?: UUID;
  reviewed_at?: Timestamp;
  rejection_reason?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}
