import { UUID, Timestamp, Currency } from "./index";

// Product types
export interface Product {
  id: UUID;
  sku: string;
  barcode?: string;
  name: string;
  description?: string;
  category_id?: UUID;
  brand_id?: UUID;
  retail_price: number;
  landing_cost?: number;
  purchase_price?: number;
  purchase_currency: Currency;
  wholesale_price?: number;
  wholesale_available: boolean;
  stock_quantity: number;
  min_stock_level: number;
  max_stock_level?: number;
  notes?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images?: string[];
  serving_size?: string;
  servings_per_container?: number;
  ingredients?: string[];
  allergens?: string[];
  expiry_tracking: boolean;
  has_variants: boolean;
  variant_type?: string;
  min_order_quantity: number;
  is_active: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface ProductVariant {
  id: UUID;
  parent_product_id: UUID;
  sku: string;
  barcode?: string;
  variant_name: string;
  variant_option: string;
  retail_price?: number;
  landing_cost?: number;
  purchase_price?: number;
  purchase_currency?: Currency;
  wholesale_price?: number;
  wholesale_available?: boolean;
  stock_quantity: number;
  min_stock_level: number;
  notes?: string;
  images?: string[];
  is_active: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Customer types
export type CustomerType = "retail" | "wholesale";
export type MembershipType = "none" | "regular" | "premium";

export interface Customer {
  id: UUID;
  customer_number?: string;
  customer_type: CustomerType;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  email?: string;
  phone?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country: string;
  assigned_coach_id?: UUID;
  coach_assigned_date?: Timestamp;
  wholesale_discount: number;
  credit_limit: number;
  payment_terms: number;
  tax_exempt: boolean;
  membership_type: MembershipType;
  membership_start_date?: string;
  membership_expiry_date?: string;
  loyalty_points: number;
  store_credit_balance: number;
  preferred_contact_method: string;
  marketing_consent: boolean;
  is_active: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Transaction types
export type TransactionType = "sale" | "return" | "wholesale_order" | "adjustment";
export type TransactionStatus = "pending" | "completed" | "cancelled" | "refunded" | "partial_refund";

export interface Transaction {
  id: UUID;
  transaction_number: string;
  customer_id?: UUID;
  staff_id?: UUID;
  device_id?: UUID;
  transaction_type: TransactionType;
  status: TransactionStatus;
  subtotal: number;
  tax_rate: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payment_method?: string;
  payment_reference?: string;
  notes?: string;
  internal_notes?: string;
  synced_at?: Timestamp;
  sync_version: number;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface TransactionItem {
  id: UUID;
  transaction_id: UUID;
  product_id: UUID;
  quantity: number;
  unit_price: number;
  discount_amount: number;
  line_total: number;
  product_name: string;
  product_sku: string;
  batch_number?: string;
  expiry_date?: string;
  created_at: Timestamp;
}
