import { UUID, Timestamp } from "./index";
import { Product, Customer, Transaction, TransactionItem } from "./database";

// POS-specific types for offline functionality
export interface POSTransaction extends Omit<Transaction, "synced_at" | "sync_version"> {
  offline_id?: string; // Local ID for offline transactions
  sync_status: "pending" | "synced" | "failed";
  retry_count: number;
  last_sync_attempt?: Timestamp;
}

export interface POSTransactionItem extends TransactionItem {
  offline_transaction_id?: string;
  batch_info?: {
    batch_id: UUID;
    batch_number: string;
    expiry_date: string;
    quantity_used: number;
  };
}

// Cart management
export interface CartItem {
  product: Product;
  variant?: {
    id: UUID;
    name: string;
    option: string;
  };
  quantity: number;
  unit_price: number;
  discount_amount: number;
  line_total: number;
  batch_info?: {
    batch_id: UUID;
    batch_number: string;
    expiry_date: string;
    available_quantity: number;
  };
}

export interface Cart {
  id: string;
  items: CartItem[];
  customer?: Customer;
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  loyalty_points_used: number;
  store_credit_used: number;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Payment processing
export interface PaymentMethod {
  id: UUID;
  name: string;
  type: "cash" | "card" | "check" | "store_credit" | "loyalty_points";
  requires_reference: boolean;
  is_active: boolean;
}

export interface Payment {
  method: PaymentMethod;
  amount: number;
  reference?: string;
  change_amount?: number;
}

// Receipt data
export interface Receipt {
  transaction_id: UUID;
  transaction_number: string;
  customer?: {
    name: string;
    membership_type: string;
    loyalty_points_earned: number;
    loyalty_points_balance: number;
  };
  items: {
    name: string;
    sku: string;
    quantity: number;
    unit_price: number;
    line_total: number;
    batch_number?: string;
    expiry_date?: string;
  }[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  payments: Payment[];
  staff_name: string;
  timestamp: Timestamp;
  store_info: {
    name: string;
    address: string;
    phone: string;
    tax_id?: string;
  };
}

// Offline sync queue
export interface SyncQueueItem {
  id: string;
  table_name: string;
  record_id: UUID;
  operation: "insert" | "update" | "delete";
  data: any;
  timestamp: Timestamp;
  retry_count: number;
  status: "pending" | "synced" | "failed";
  error_message?: string;
}

// POS Settings
export interface POSSettings {
  store_info: {
    name: string;
    address: string;
    phone: string;
    tax_id?: string;
  };
  tax_rate: number;
  receipt_printer: {
    enabled: boolean;
    printer_name?: string;
    paper_width: number;
  };
  barcode_scanner: {
    enabled: boolean;
    sound_enabled: boolean;
  };
  offline_mode: {
    enabled: boolean;
    sync_interval: number; // minutes
    max_offline_transactions: number;
  };
  loyalty_program: {
    enabled: boolean;
    points_per_dollar: number;
    redemption_rate: number; // points per dollar
  };
}

// Batch selection for FIFO
export interface BatchSelection {
  batch_id: UUID;
  batch_number: string;
  expiry_date: string;
  available_quantity: number;
  unit_cost: number;
  quantity_to_use: number;
}
