// Core types
export * from "./database";
export * from "./api";
export * from "./auth";
export * from "./pos";
export * from "./wholesale";
export * from "./coach";
export * from "./loyalty";

// Common utility types
export type UUID = string;
export type Timestamp = string;
export type Currency = "AWG" | "USD" | "EUR" | "GBP" | "CAD";

// Status enums
export type Status = "active" | "inactive" | "pending" | "completed" | "cancelled";

// Pagination
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API Response wrapper
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: Timestamp;
    requestId: string;
  };
}

// Error types
export interface AppError {
  code: string;
  message: string;
  statusCode: number;
  details?: any;
}

// Configuration types
export interface AppConfig {
  database: {
    url: string;
    maxConnections: number;
  };
  auth: {
    jwtSecret: string;
    sessionTimeout: number;
  };
  features: {
    offlineMode: boolean;
    aiForecasting: boolean;
    coachProgram: boolean;
  };
}
