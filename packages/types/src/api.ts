import { UUID, Pa<PERSON>ationParams, ApiResponse } from "./index";
import { Product, Customer, Transaction } from "./database";

// Product API types
export interface CreateProductRequest {
  sku: string;
  barcode?: string;
  name: string;
  description?: string;
  category_id?: UUID;
  brand_id?: UUID;
  retail_price: number;
  wholesale_price?: number;
  cost_price?: number;
  min_stock_level?: number;
  expiry_tracking?: boolean;
  has_variants?: boolean;
  variant_type?: string;
}

export interface UpdateProductRequest extends Partial<CreateProductRequest> {
  id: UUID;
}

export interface ProductSearchParams extends PaginationParams {
  search?: string;
  category_id?: UUID;
  brand_id?: UUID;
  low_stock?: boolean;
  wholesale_only?: boolean;
  has_variants?: boolean;
}

// Customer API types
export interface CreateCustomerRequest {
  customer_type: "retail" | "wholesale";
  first_name?: string;
  last_name?: string;
  company_name?: string;
  email?: string;
  phone?: string;
  address_line1?: string;
  city?: string;
  membership_type?: "none" | "regular" | "premium";
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {
  id: UUID;
}

export interface CustomerSearchParams extends PaginationParams {
  search?: string;
  customer_type?: "retail" | "wholesale";
  membership_type?: "none" | "regular" | "premium";
  has_coach?: boolean;
}

// Transaction API types
export interface CreateTransactionRequest {
  customer_id?: UUID;
  transaction_type: "sale" | "return" | "wholesale_order";
  items: {
    product_id: UUID;
    quantity: number;
    unit_price: number;
    discount_amount?: number;
  }[];
  payment_method?: string;
  payment_reference?: string;
  notes?: string;
}

export interface TransactionSearchParams extends PaginationParams {
  customer_id?: UUID;
  transaction_type?: "sale" | "return" | "wholesale_order";
  status?: "pending" | "completed" | "cancelled";
  date_from?: string;
  date_to?: string;
  staff_id?: UUID;
}

// Inventory API types
export interface StockAdjustmentRequest {
  product_id: UUID;
  quantity_change: number;
  reason: string;
  notes?: string;
}

export interface BatchCreateRequest {
  product_id: UUID;
  variant_id?: UUID;
  batch_number: string;
  expiry_date: string;
  quantity_received: number;
  unit_cost: number;
}

// Analytics API types
export interface SalesAnalyticsParams {
  date_from: string;
  date_to: string;
  group_by?: "day" | "week" | "month";
  customer_type?: "retail" | "wholesale";
}

export interface SalesAnalyticsResponse {
  total_sales: number;
  total_transactions: number;
  average_transaction: number;
  top_products: {
    product_id: UUID;
    product_name: string;
    quantity_sold: number;
    revenue: number;
  }[];
  sales_by_period: {
    period: string;
    sales: number;
    transactions: number;
  }[];
}

// Sync API types
export interface SyncRequest {
  device_id: UUID;
  last_sync?: string;
  operations: {
    table_name: string;
    record_id: UUID;
    operation: "insert" | "update" | "delete";
    data?: any;
    timestamp: string;
  }[];
}

export interface SyncResponse {
  success: boolean;
  conflicts?: {
    record_id: UUID;
    table_name: string;
    server_data: any;
    client_data: any;
  }[];
  updates?: {
    table_name: string;
    records: any[];
  }[];
  last_sync: string;
}
