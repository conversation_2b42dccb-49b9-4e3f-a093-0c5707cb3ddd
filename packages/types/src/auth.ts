import { UUID, Timestamp } from "./index";

export type UserRole = "admin" | "staff" | "accountant" | "wholesale_client";

export interface User {
  id: UUID;
  email: string;
  role: UserR<PERSON>;
  first_name?: string;
  last_name?: string;
  phone?: string;
  is_active: boolean;
  last_login?: Timestamp;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface AuthSession {
  user: User;
  access_token: string;
  refresh_token: string;
  expires_at: Timestamp;
}

export interface LoginRequest {
  email: string;
  password: string;
  device_id?: UUID;
}

export interface LoginResponse {
  session: AuthSession;
  permissions: string[];
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  role?: UserRole;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordUpdateRequest {
  current_password: string;
  new_password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

// Permission types
export interface Permission {
  id: UUID;
  name: string;
  description: string;
  resource: string;
  action: string;
}

export interface RolePermission {
  role: UserRole;
  permissions: Permission[];
}

// Device authentication for POS tablets
export interface DeviceRegistration {
  device_name: string;
  device_type: "pos_tablet" | "sales_tablet" | "delivery_mobile";
  device_identifier: string;
}

export interface DeviceAuthResponse {
  device_id: UUID;
  device_token: string;
  expires_at: Timestamp;
}
