import { UUID, Timestamp, Currency } from "./index";
import { Customer, Product } from "./database";

// Wholesale-specific customer data
export interface WholesaleCustomer extends Customer {
  customer_type: "wholesale";
  company_name: string;
  wholesale_discount: number;
  credit_limit: number;
  payment_terms: number;
  tax_exempt: boolean;
  account_manager_id?: UUID;
}

// Wholesale pricing
export interface WholesalePricing {
  product_id: UUID;
  customer_id?: UUID; // null for default pricing
  min_quantity: number;
  unit_price: number;
  currency: Currency;
  effective_date: string;
  expiry_date?: string;
  is_active: boolean;
}

// Quote system
export type QuoteStatus = "draft" | "sent" | "accepted" | "rejected" | "expired";

export interface WholesaleQuote {
  id: UUID;
  quote_number: string;
  customer_id: UUID;
  status: QuoteStatus;
  valid_until: string;
  items: WholesaleQuoteItem[];
  subtotal: number;
  tax_amount: number;
  discount_amount: number;
  total_amount: number;
  currency: Currency;
  notes?: string;
  internal_notes?: string;
  created_by: UUID;
  sent_at?: Timestamp;
  accepted_at?: Timestamp;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface WholesaleQuoteItem {
  id: UUID;
  quote_id: UUID;
  product_id: UUID;
  quantity: number;
  unit_price: number;
  discount_percentage: number;
  line_total: number;
  product_name: string;
  product_sku: string;
  availability_status: "available" | "limited" | "out_of_stock";
  estimated_delivery?: string;
}

// Order system
export type OrderStatus = "pending" | "confirmed" | "processing" | "shipped" | "delivered" | "cancelled";

export interface WholesaleOrder {
  id: UUID;
  order_number: string;
  customer_id: UUID;
  quote_id?: UUID;
  status: OrderStatus;
  order_date: string;
  requested_delivery_date?: string;
  estimated_delivery_date?: string;
  actual_delivery_date?: string;
  items: WholesaleOrderItem[];
  subtotal: number;
  tax_amount: number;
  shipping_cost: number;
  discount_amount: number;
  total_amount: number;
  currency: Currency;
  payment_terms: number;
  payment_status: "pending" | "partial" | "paid" | "overdue";
  shipping_address: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
  tracking_number?: string;
  notes?: string;
  internal_notes?: string;
  created_at: Timestamp;
  updated_at: Timestamp;
}

export interface WholesaleOrderItem {
  id: UUID;
  order_id: UUID;
  product_id: UUID;
  quantity_ordered: number;
  quantity_shipped: number;
  unit_price: number;
  discount_percentage: number;
  line_total: number;
  product_name: string;
  product_sku: string;
  batch_numbers?: string[];
  expiry_dates?: string[];
}

// Inventory reservation
export interface InventoryReservation {
  id: UUID;
  product_id: UUID;
  variant_id?: UUID;
  customer_id: UUID;
  quantity_reserved: number;
  reservation_type: "quote" | "order" | "manual";
  reference_id: UUID; // quote_id or order_id
  reference_number: string;
  reserved_until: Timestamp;
  status: "active" | "fulfilled" | "expired" | "cancelled";
  notes?: string;
  created_at: Timestamp;
  released_at?: Timestamp;
}

// Customer portal types
export interface CustomerPortalSession {
  customer_id: UUID;
  access_token: string;
  expires_at: Timestamp;
}

export interface CustomerDashboard {
  customer: WholesaleCustomer;
  recent_orders: WholesaleOrder[];
  pending_quotes: WholesaleQuote[];
  account_summary: {
    credit_limit: number;
    credit_used: number;
    credit_available: number;
    overdue_amount: number;
    last_payment_date?: string;
  };
  favorite_products: Product[];
}

// Wholesale analytics
export interface WholesaleAnalytics {
  customer_id: UUID;
  period: {
    start_date: string;
    end_date: string;
  };
  total_orders: number;
  total_value: number;
  average_order_value: number;
  top_products: {
    product_id: UUID;
    product_name: string;
    quantity_ordered: number;
    total_value: number;
  }[];
  order_frequency: {
    month: string;
    order_count: number;
    total_value: number;
  }[];
}
