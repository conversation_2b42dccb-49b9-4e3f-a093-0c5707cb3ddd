import { UUID, Timestamp } from "./index";
import { MembershipType } from "./database";

// Loyalty program types
export interface LoyaltyProgram {
  id: UUID;
  name: string;
  description: string;
  points_per_dollar: number;
  redemption_rate: number; // points per dollar value
  minimum_redemption: number;
  maximum_redemption?: number;
  is_active: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Membership plans
export interface MembershipPlan {
  id: UUID;
  plan_type: MembershipType;
  plan_name: string;
  annual_fee: number;
  discount_percentage: number;
  points_multiplier: number;
  benefits: {
    free_shipping?: boolean;
    priority_support?: boolean;
    exclusive_products?: boolean;
    birthday_bonus?: number;
    referral_bonus?: number;
  };
  is_active: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Loyalty transactions
export type LoyaltyTransactionType = "earned" | "redeemed" | "adjustment" | "expired" | "bonus";

export interface LoyaltyTransaction {
  id: UUID;
  customer_id: UUID;
  transaction_id?: UUID; // null for manual adjustments
  transaction_type: LoyaltyTransactionType;
  points_change: number; // positive for earned, negative for redeemed
  points_balance_before: number;
  points_balance_after: number;
  description: string;
  reference_amount?: number; // purchase amount that generated points
  expiry_date?: string; // for points that expire
  created_at: Timestamp;
}

// Store credit system
export type CreditTransactionType = "issued" | "used" | "adjustment" | "expired" | "refund";

export interface StoreCreditTransaction {
  id: UUID;
  customer_id: UUID;
  transaction_id?: UUID; // null for manual adjustments
  transaction_type: CreditTransactionType;
  amount_change: number; // positive for added, negative for used
  balance_before: number;
  balance_after: number;
  description: string;
  reference_number?: string; // return number, adjustment reference
  expiry_date?: string; // null = no expiry
  created_by?: UUID; // staff member for manual adjustments
  created_at: Timestamp;
}

// Customer loyalty summary
export interface CustomerLoyaltySummary {
  customer_id: UUID;
  membership_type: MembershipType;
  membership_start_date?: string;
  membership_expiry_date?: string;
  loyalty_points_balance: number;
  store_credit_balance: number;
  lifetime_points_earned: number;
  lifetime_points_redeemed: number;
  lifetime_purchases: number;
  lifetime_savings: number;
  current_tier_benefits: {
    discount_percentage: number;
    points_multiplier: number;
    free_shipping: boolean;
    priority_support: boolean;
  };
  next_tier?: {
    name: string;
    required_spending: number;
    additional_benefits: string[];
  };
}

// Loyalty rewards/offers
export interface LoyaltyReward {
  id: UUID;
  name: string;
  description: string;
  reward_type: "discount" | "free_product" | "store_credit" | "bonus_points";
  points_required: number;
  discount_percentage?: number;
  discount_amount?: number;
  free_product_id?: UUID;
  store_credit_amount?: number;
  bonus_points_amount?: number;
  minimum_purchase?: number;
  maximum_uses_per_customer?: number;
  valid_from: string;
  valid_until: string;
  is_active: boolean;
  created_at: Timestamp;
  updated_at: Timestamp;
}

// Loyalty reward redemptions
export interface LoyaltyRedemption {
  id: UUID;
  customer_id: UUID;
  reward_id: UUID;
  transaction_id?: UUID;
  points_used: number;
  discount_applied?: number;
  store_credit_issued?: number;
  bonus_points_awarded?: number;
  redeemed_at: Timestamp;
}

// Loyalty analytics
export interface LoyaltyAnalytics {
  period: {
    start_date: string;
    end_date: string;
  };
  program_metrics: {
    total_members: number;
    active_members: number;
    new_members: number;
    points_issued: number;
    points_redeemed: number;
    store_credits_issued: number;
    store_credits_used: number;
    total_savings_provided: number;
  };
  membership_breakdown: {
    membership_type: MembershipType;
    member_count: number;
    average_points_balance: number;
    average_purchase_frequency: number;
    total_revenue: number;
  }[];
  top_rewards: {
    reward_id: UUID;
    reward_name: string;
    redemption_count: number;
    points_redeemed: number;
  }[];
  customer_segments: {
    segment: "high_value" | "regular" | "at_risk" | "new";
    customer_count: number;
    average_points_balance: number;
    average_lifetime_value: number;
  }[];
}

// Birthday and anniversary bonuses
export interface LoyaltyBonus {
  id: UUID;
  customer_id: UUID;
  bonus_type: "birthday" | "anniversary" | "milestone" | "referral";
  points_awarded: number;
  store_credit_awarded?: number;
  description: string;
  awarded_date: string;
  expires_date?: string;
  is_claimed: boolean;
  claimed_at?: Timestamp;
  created_at: Timestamp;
}
