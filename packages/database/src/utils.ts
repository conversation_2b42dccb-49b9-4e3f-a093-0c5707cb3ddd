import type { SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "./types/supabase";

// Utility functions for database operations

export function handleSupabaseError(error: any): never {
  console.error("Supabase error:", error);
  throw new Error(error.message || "Database operation failed");
}

export async function withTransaction<T>(
  client: SupabaseClient<Database>,
  callback: (client: SupabaseClient<Database>) => Promise<T>
): Promise<T> {
  // Note: Supabase doesn't support transactions in the traditional sense
  // This is a placeholder for future transaction support
  return callback(client);
}

export function buildPaginationQuery<T>(
  query: any,
  page: number = 1,
  limit: number = 10
) {
  const offset = (page - 1) * limit;
  return query.range(offset, offset + limit - 1);
}

export function buildSearchQuery(
  query: any,
  searchTerm: string,
  searchColumns: string[]
) {
  if (!searchTerm) return query;
  
  const searchConditions = searchColumns
    .map(column => `${column}.ilike.%${searchTerm}%`)
    .join(",");
  
  return query.or(searchConditions);
}

export function buildSortQuery(
  query: any,
  sortBy?: string,
  sortOrder: "asc" | "desc" = "asc"
) {
  if (!sortBy) return query;
  return query.order(sortBy, { ascending: sortOrder === "asc" });
}

// Type-safe query builder helpers
export type QueryBuilder<T> = {
  select: (columns?: string) => QueryBuilder<T>;
  eq: (column: keyof T, value: any) => QueryBuilder<T>;
  neq: (column: keyof T, value: any) => QueryBuilder<T>;
  gt: (column: keyof T, value: any) => QueryBuilder<T>;
  gte: (column: keyof T, value: any) => QueryBuilder<T>;
  lt: (column: keyof T, value: any) => QueryBuilder<T>;
  lte: (column: keyof T, value: any) => QueryBuilder<T>;
  like: (column: keyof T, pattern: string) => QueryBuilder<T>;
  ilike: (column: keyof T, pattern: string) => QueryBuilder<T>;
  in: (column: keyof T, values: any[]) => QueryBuilder<T>;
  is: (column: keyof T, value: null | boolean) => QueryBuilder<T>;
  order: (column: keyof T, options?: { ascending?: boolean }) => QueryBuilder<T>;
  limit: (count: number) => QueryBuilder<T>;
  range: (from: number, to: number) => QueryBuilder<T>;
};

// Date utilities
export function formatDateForDB(date: Date): string {
  return date.toISOString();
}

export function parseDBDate(dateString: string): Date {
  return new Date(dateString);
}

// UUID validation
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

// Currency formatting
export function formatCurrency(amount: number, currency: string = "AWG"): string {
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency === "AWG" ? "USD" : currency, // AWG not supported, use USD format
    minimumFractionDigits: 2,
  });
  
  const formatted = formatter.format(amount);
  return currency === "AWG" ? formatted.replace("$", "ƒ") : formatted;
}
