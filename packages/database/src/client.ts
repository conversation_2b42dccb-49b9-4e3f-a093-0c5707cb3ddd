import { createClient as createSupabaseClient, SupabaseClient } from "@supabase/supabase-js";
import type { Database } from "./types/supabase";

// Default client for server-side usage
let supabase: SupabaseClient<Database> | null = null;

export function createClient(
  supabaseUrl?: string,
  supabaseKey?: string,
  options?: {
    auth?: {
      persistSession?: boolean;
      autoRefreshToken?: boolean;
    };
    global?: {
      headers?: Record<string, string>;
    };
  }
): SupabaseClient<Database> {
  const url = supabaseUrl || process.env.NEXT_PUBLIC_SUPABASE_URL || process.env.SUPABASE_URL;
  const key = supabaseKey || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

  if (!url || !key) {
    throw new Error("Supabase URL and key are required");
  }

  return createSupabaseClient<Database>(url, key, {
    auth: {
      persistSession: options?.auth?.persistSession ?? true,
      autoRefreshToken: options?.auth?.autoRefreshToken ?? true,
    },
    global: {
      headers: options?.global?.headers || {},
    },
  });
}

// Initialize default client
export function getSupabaseClient(): SupabaseClient<Database> {
  if (!supabase) {
    supabase = createClient();
  }
  return supabase;
}

// Export default client
export { supabase };

// Initialize on import
if (typeof window === "undefined") {
  // Server-side initialization
  supabase = createClient();
}
