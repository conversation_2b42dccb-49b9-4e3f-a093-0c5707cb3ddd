{"name": "@nutripro/database", "version": "0.0.0", "private": true, "description": "Database client and utilities for NutriPro", "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./client": "./src/client.ts", "./queries": "./src/queries/index.ts", "./migrations": "./src/migrations/index.ts", "./seed": "./src/seed/index.ts"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "clean": "rm -rf dist", "db:generate": "supabase gen types typescript --local > src/types/supabase.ts", "db:push": "supabase db push", "db:seed": "tsx src/seed/index.ts", "db:studio": "supabase studio"}, "dependencies": {"@supabase/supabase-js": "^2.38.5", "@nutripro/types": "workspace:*"}, "devDependencies": {"@nutripro/eslint-config": "workspace:*", "@nutripro/tsconfig": "workspace:*", "eslint": "^8.56.0", "tsx": "^4.7.0", "typescript": "^5.3.3"}}