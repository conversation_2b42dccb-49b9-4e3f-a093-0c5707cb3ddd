{"$schema": "https://json.schemastore.org/tsconfig", "display": "Node.js", "extends": "./base.json", "compilerOptions": {"target": "es2022", "lib": ["es2022"], "module": "commonjs", "moduleResolution": "node", "allowJs": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/lib/*": ["./src/lib/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/config/*": ["./src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"]}