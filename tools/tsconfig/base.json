{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "target": "es2022", "module": "esnext", "lib": ["es2022"], "allowJs": true, "checkJs": false, "incremental": true, "noEmit": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "exclude": ["node_modules", "dist", "build", ".next", "coverage", "**/*.test.*", "**/*.spec.*"]}