{"$schema": "https://json.schemastore.org/tsconfig", "display": "React Native", "extends": "./base.json", "compilerOptions": {"target": "esnext", "lib": ["esnext"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/utils/*": ["./src/utils/*"], "@/types/*": ["./src/types/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/screens/*": ["./src/screens/*"], "@/navigation/*": ["./src/navigation/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"]}