{"name": "@nutripro/eslint-config", "version": "0.0.0", "private": true, "description": "Shared ESLint configurations for NutriPro", "main": "index.js", "files": ["base.js", "nextjs.js", "react-native.js", "node.js"], "dependencies": {"@next/eslint-plugin-next": "^14.0.4", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0"}, "devDependencies": {"eslint": "^8.56.0", "typescript": "^5.3.3"}}